using CategoryData = JT.Client.Models.CategoryData;
namespace JT.Business.Models;

public partial record Category
{
	internal Category(CategoryData? category)
	{
		Id = category?.Id ?? 0;
		UrlIcon = category?.UrlIcon;
		Name = category?.Name;
		Color = category?.Color;
		Description = category?.Description;
	}

	public int Id { get; init; }
	public string? UrlIcon { get; init; }
	public string? Name { get; init; }
	public string? Color { get; init; }
	public string? Description { get; init; }

	// Computed properties
	public string DisplayName => Name ?? "Unknown Category";
	public string IconPath => UrlIcon ?? "ms-appx:///Assets/Categories/default.png";
	public string CategoryColor => Color ?? "#6C757D";

	internal CategoryData ToData() => new()
	{
		Id = Id,
		UrlIcon = UrlIcon,
		Name = Name,
		Color = Color,
		Description = Description
	};
}
