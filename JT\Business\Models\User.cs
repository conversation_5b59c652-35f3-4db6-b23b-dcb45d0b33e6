using UserData = JT.Client.Models.UserData;
namespace JT.Business.Models;

public partial record User
{
	internal User(UserData user)
	{
		Id = (Guid)user.Id;
		FirstName = user.FirstName;
		SecondName = user.SecondName;
		Tribe = user.Tribe;
		FullName = user.FullName;
		Email = user.Email;
		PhoneNumber = user.PhoneNumber;
		Password = user.Password;
		Age = user.Age ?? 0;
		Gender = user.Gender;
		CurrentEmployerLocation = user.CurrentEmployerLocation;
		CurrentEmployerCity = user.CurrentEmployerCity;
		CurrentEmployerState = user.CurrentEmployerState;
		CurrentSalaryGrade = user.CurrentSalaryGrade;
		EducationalQualification = user.EducationalQualification;
		SubscriptionTier = user.SubscriptionTier;
		TokenBalance = user.TokenBalance ?? 0;
		ProfileImageUrl = user.ProfileImageUrl;
		Description = user.Description;
		JoinDate = user.JoinDate ?? DateTime.MinValue;
		IsActive = user.IsActive ?? true;
		ReferralCode = user.ReferralCode;
		InvitedFriends = user.InvitedFriends ?? 0;
		CompletedTransfers = user.CompletedTransfers ?? 0;
		IsCurrent = user.IsCurrent ?? false;

		// Additional fields from reference project
		CurrentEmployer = user.CurrentEmployer;
		CurrentPosition = user.CurrentPosition;
		YearsOfExperience = user.YearsOfExperience ?? 0;
		Skills = user.Skills?.ToImmutableList() ?? ImmutableList<string>.Empty;
		Languages = user.Languages?.ToImmutableList() ?? ImmutableList<string>.Empty;
		Certifications = user.Certifications?.ToImmutableList() ?? ImmutableList<string>.Empty;
		PreferredWorkType = user.PreferredWorkType;
		IsAvailableForTransfer = user.IsAvailableForTransfer ?? true;
		LinkedInProfile = user.LinkedInProfile;
		Portfolio = user.Portfolio;
		ResumeUrl = user.ResumeUrl;
		Bio = user.Bio;
		LastLoginAt = user.LastLoginAt ?? DateTime.MinValue;
		UpdatedAt = user.UpdatedAt ?? DateTime.MinValue;
	}

	public Guid Id { get; init; }
	public string? FirstName { get; init; }
	public string? SecondName { get; init; }
	public string? Tribe { get; init; }
	public string? FullName { get; init; }
	public string? Email { get; init; }
	public string? PhoneNumber { get; init; }
	public string? Password { get; init; }
	public int Age { get; init; }
	public string? Gender { get; init; }
	public string? CurrentEmployerLocation { get; init; }
	public string? CurrentEmployerCity { get; init; }
	public string? CurrentEmployerState { get; init; }
	public string? CurrentSalaryGrade { get; init; }
	public string? EducationalQualification { get; init; }
	public string? SubscriptionTier { get; init; }
	public int TokenBalance { get; init; }
	public string? ProfileImageUrl { get; init; }
	public string? Description { get; init; }
	public DateTime JoinDate { get; init; }
	public bool IsActive { get; init; }
	public string? ReferralCode { get; init; }
	public int InvitedFriends { get; init; }
	public int CompletedTransfers { get; init; }
	public bool IsCurrent { get; init; }

	// Additional fields from reference project
	public string? CurrentEmployer { get; init; }
	public string? CurrentPosition { get; init; }
	public int YearsOfExperience { get; init; }
	public IImmutableList<string> Skills { get; init; }
	public IImmutableList<string> Languages { get; init; }
	public IImmutableList<string> Certifications { get; init; }
	public string? PreferredWorkType { get; init; } // office, remote, hybrid
	public bool IsAvailableForTransfer { get; init; }
	public string? LinkedInProfile { get; init; }
	public string? Portfolio { get; init; }
	public string? ResumeUrl { get; init; }
	public string? Bio { get; init; }
	public DateTime LastLoginAt { get; init; }
	public DateTime UpdatedAt { get; init; }

	internal UserData ToData() => new()
	{
		Id = Id,
		FirstName = FirstName,
		SecondName = SecondName,
		Tribe = Tribe,
		FullName = FullName,
		Email = Email,
		PhoneNumber = PhoneNumber,
		Password = Password,
		Age = Age,
		Gender = Gender,
		CurrentEmployerLocation = CurrentEmployerLocation,
		CurrentEmployerCity = CurrentEmployerCity,
		CurrentEmployerState = CurrentEmployerState,
		CurrentSalaryGrade = CurrentSalaryGrade,
		EducationalQualification = EducationalQualification,
		SubscriptionTier = SubscriptionTier,
		TokenBalance = TokenBalance,
		ProfileImageUrl = ProfileImageUrl,
		Description = Description,
		JoinDate = JoinDate,
		IsActive = IsActive,
		ReferralCode = ReferralCode,
		InvitedFriends = InvitedFriends,
		CompletedTransfers = CompletedTransfers,
		IsCurrent = IsCurrent,

		// Additional fields
		CurrentEmployer = CurrentEmployer,
		CurrentPosition = CurrentPosition,
		YearsOfExperience = YearsOfExperience,
		Skills = Skills.ToList(),
		Languages = Languages.ToList(),
		Certifications = Certifications.ToList(),
		PreferredWorkType = PreferredWorkType,
		IsAvailableForTransfer = IsAvailableForTransfer,
		LinkedInProfile = LinkedInProfile,
		Portfolio = Portfolio,
		ResumeUrl = ResumeUrl,
		Bio = Bio,
		LastLoginAt = LastLoginAt,
		UpdatedAt = UpdatedAt
	};
}
