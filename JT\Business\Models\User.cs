using UserData = JT.Client.Models.UserData;
namespace JT.Business.Models;

public partial record User
{
	internal User(UserData user)
	{
		Id = (Guid)user.Id;
		FirstName = user.FirstName;
		SecondName = user.SecondName;
		Tribe = user.Tribe;
		FullName = user.FullName;
		Email = user.Email;
		PhoneNumber = user.PhoneNumber;
		Password = user.Password;
		Age = user.Age ?? 0;
		Gender = user.Gender;
		CurrentEmployerLocation = user.CurrentEmployerLocation;
		CurrentEmployerCity = user.CurrentEmployerCity;
		CurrentEmployerState = user.CurrentEmployerState;
		CurrentSalaryGrade = user.CurrentSalaryGrade;
		EducationalQualification = user.EducationalQualification;
		SubscriptionTier = user.SubscriptionTier;
		TokenBalance = user.TokenBalance ?? 0;
		ProfileImageUrl = user.ProfileImageUrl;
		Description = user.Description;
		JoinDate = user.JoinDate ?? DateTime.MinValue;
		IsActive = user.IsActive ?? true;
		ReferralCode = user.ReferralCode;
		InvitedFriends = user.InvitedFriends ?? 0;
		CompletedTransfers = user.CompletedTransfers ?? 0;
		IsCurrent = user.IsCurrent ?? false;
	}

	public Guid Id { get; init; }
	public string? FirstName { get; init; }
	public string? SecondName { get; init; }
	public string? Tribe { get; init; }
	public string? FullName { get; init; }
	public string? Email { get; init; }
	public string? PhoneNumber { get; init; }
	public string? Password { get; init; }
	public int Age { get; init; }
	public string? Gender { get; init; }
	public string? CurrentEmployerLocation { get; init; }
	public string? CurrentEmployerCity { get; init; }
	public string? CurrentEmployerState { get; init; }
	public string? CurrentSalaryGrade { get; init; }
	public string? EducationalQualification { get; init; }
	public string? SubscriptionTier { get; init; }
	public int TokenBalance { get; init; }
	public string? ProfileImageUrl { get; init; }
	public string? Description { get; init; }
	public DateTime JoinDate { get; init; }
	public bool IsActive { get; init; }
	public string? ReferralCode { get; init; }
	public int InvitedFriends { get; init; }
	public int CompletedTransfers { get; init; }
	public bool IsCurrent { get; init; }

	internal UserData ToData() => new()
	{
		Id = Id,
		FirstName = FirstName,
		SecondName = SecondName,
		Tribe = Tribe,
		FullName = FullName,
		Email = Email,
		PhoneNumber = PhoneNumber,
		Password = Password,
		Age = Age,
		Gender = Gender,
		CurrentEmployerLocation = CurrentEmployerLocation,
		CurrentEmployerCity = CurrentEmployerCity,
		CurrentEmployerState = CurrentEmployerState,
		CurrentSalaryGrade = CurrentSalaryGrade,
		EducationalQualification = EducationalQualification,
		SubscriptionTier = SubscriptionTier,
		TokenBalance = TokenBalance,
		ProfileImageUrl = ProfileImageUrl,
		Description = Description,
		JoinDate = JoinDate,
		IsActive = IsActive,
		ReferralCode = ReferralCode,
		InvitedFriends = InvitedFriends,
		CompletedTransfers = CompletedTransfers,
		IsCurrent = IsCurrent
	};
}
