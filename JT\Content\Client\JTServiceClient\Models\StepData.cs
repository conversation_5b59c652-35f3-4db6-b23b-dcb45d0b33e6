// <auto-generated/>
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace JT.Client.Models
{
	[global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.16.0")]
#pragma warning disable CS1591
	public partial class StepData : IParsable
#pragma warning restore CS1591
	{
		/// <summary>The cookTime property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
		public global::JT.Client.Models.TimeSpanObject? CookTime { get; set; }
#nullable restore
#else
        public global::JT.Client.Models.TimeSpanObject CookTime { get; set; }
#endif
		/// <summary>The cookware property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
		public List<string>? Cookware { get; set; }
#nullable restore
#else
        public List<string> Cookware { get; set; }
#endif
		/// <summary>The description property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
		public string? Description { get; set; }
#nullable restore
#else
        public string Description { get; set; }
#endif
		/// <summary>The ingredients property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
		public List<string>? Ingredients { get; set; }
#nullable restore
#else
        public List<string> Ingredients { get; set; }
#endif
		/// <summary>The name property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
		public string? Name { get; set; }
#nullable restore
#else
        public string Name { get; set; }
#endif
		/// <summary>The number property</summary>
		public int? Number { get; set; }
		/// <summary>The urlVideo property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
		public string? UrlVideo { get; set; }
#nullable restore
#else
        public string UrlVideo { get; set; }
#endif
		/// <summary>
		/// Creates a new instance of the appropriate class based on discriminator value
		/// </summary>
		/// <returns>A <see cref="global::JT.Client.Models.StepData"/></returns>
		/// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
		public static global::JT.Client.Models.StepData CreateFromDiscriminatorValue(IParseNode parseNode)
		{
			_ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
			return new global::JT.Client.Models.StepData();
		}
		/// <summary>
		/// The deserialization information for the current model
		/// </summary>
		/// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
		public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
		{
			return new Dictionary<string, Action<IParseNode>>
			{
				{ "cookTime", n => { CookTime = n.GetObjectValue<global::JT.Client.Models.TimeSpanObject>(global::JT.Client.Models.TimeSpanObject.CreateFromDiscriminatorValue); } },
				{ "cookware", n => { Cookware = n.GetCollectionOfPrimitiveValues<string>()?.AsList(); } },
				{ "description", n => { Description = n.GetStringValue(); } },
				{ "ingredients", n => { Ingredients = n.GetCollectionOfPrimitiveValues<string>()?.AsList(); } },
				{ "name", n => { Name = n.GetStringValue(); } },
				{ "number", n => { Number = n.GetIntValue(); } },
				{ "urlVideo", n => { UrlVideo = n.GetStringValue(); } },
			};
		}
		/// <summary>
		/// Serializes information the current object
		/// </summary>
		/// <param name="writer">Serialization writer to use to serialize this model</param>
		public virtual void Serialize(ISerializationWriter writer)
		{
			_ = writer ?? throw new ArgumentNullException(nameof(writer));
			writer.WriteObjectValue<global::JT.Client.Models.TimeSpanObject>("cookTime", CookTime);
			writer.WriteCollectionOfPrimitiveValues<string>("cookware", Cookware);
			writer.WriteStringValue("description", Description);
			writer.WriteCollectionOfPrimitiveValues<string>("ingredients", Ingredients);
			writer.WriteStringValue("name", Name);
			writer.WriteIntValue("number", Number);
			writer.WriteStringValue("urlVideo", UrlVideo);
		}
	}
}
