{"openapi": "3.0.1", "info": {"title": "JobTransfer API", "version": "v1", "description": "API for Omani Job Transfer Application with subscription tiers, token system, and payment processing"}, "servers": [{"url": "https://localhost:5002", "description": "Local development server"}], "paths": {"/api/transferrequest": {"get": {"summary": "Get all transfer requests", "operationId": "GetTransferRequests", "tags": ["TransferRequest"], "responses": {"200": {"description": "A list of transfer requests", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransferRequest"}}}}}}}, "post": {"summary": "Create a new transfer request", "operationId": "CreateTransferRequest", "tags": ["TransferRequest"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequest"}}}}, "responses": {"201": {"description": "Transfer request created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequest"}}}}}}}, "/api/transferrequest/{id}": {"get": {"summary": "Get transfer request by ID", "operationId": "GetTransferRequestById", "tags": ["TransferRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Transfer request found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequest"}}}}, "404": {"description": "Transfer request not found"}}}, "put": {"summary": "Update transfer request", "operationId": "UpdateTransferRequest", "tags": ["TransferRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequest"}}}}, "responses": {"200": {"description": "Transfer request updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequest"}}}}}}, "delete": {"summary": "Delete transfer request", "operationId": "DeleteTransferRequest", "tags": ["TransferRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"204": {"description": "Transfer request deleted successfully"}}}}, "/api/subscription/plans": {"get": {"summary": "Get all subscription plans", "operationId": "GetSubscriptionPlans", "tags": ["Subscription"], "responses": {"200": {"description": "A list of subscription plans", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionPlan"}}}}}}}}, "/api/subscription/plans/{planId}": {"get": {"summary": "Get subscription plan by ID", "operationId": "GetSubscriptionPlan", "tags": ["Subscription"], "parameters": [{"name": "planId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Subscription plan found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionPlan"}}}}, "404": {"description": "Subscription plan not found"}}}}, "/api/token/user/{userId}/transactions": {"get": {"summary": "Get user token transactions", "operationId": "GetUserTokenTransactions", "tags": ["Token"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User token transactions", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TokenTransaction"}}}}}}}}, "/api/token/user/{userId}/balance": {"get": {"summary": "Get user token balance", "operationId": "GetUserTokenBalance", "tags": ["Token"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User token balance", "content": {"application/json": {"schema": {"type": "integer"}}}}}}}, "/api/token/user/{userId}/add": {"post": {"summary": "Add tokens to user account", "operationId": "AddTokens", "tags": ["Token"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddTokensRequest"}}}}, "responses": {"200": {"description": "Tokens added successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenTransaction"}}}}}}}, "/api/token/user/{userId}/deduct": {"post": {"summary": "Deduct tokens from user account", "operationId": "DeductTokens", "tags": ["Token"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeductTokensRequest"}}}}, "responses": {"200": {"description": "Tokens deducted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenTransaction"}}}}, "400": {"description": "Insufficient token balance"}}}}, "/api/user": {"get": {"summary": "Get all users", "operationId": "GetUsers", "tags": ["User"], "responses": {"200": {"description": "A list of users", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}}}}, "post": {"summary": "Create a new user", "operationId": "CreateUser", "tags": ["User"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "responses": {"201": {"description": "User created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}}}, "/api/user/{id}": {"get": {"summary": "Get user by ID", "operationId": "GetUserById", "tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "404": {"description": "User not found"}}}, "put": {"summary": "Update user", "operationId": "UpdateUser", "tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "responses": {"200": {"description": "User updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}}}, "/api/promotion": {"get": {"summary": "Get all promotions", "operationId": "GetPromotions", "tags": ["Promotion"], "responses": {"200": {"description": "A list of promotions", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Promotion"}}}}}}}, "post": {"summary": "Create a new promotion", "operationId": "CreatePromotion", "tags": ["Promotion"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Promotion"}}}}, "responses": {"201": {"description": "Promotion created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Promotion"}}}}}}}, "/api/promotion/{id}": {"get": {"summary": "Get promotion by ID", "operationId": "GetPromotionById", "tags": ["Promotion"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Promotion found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Promotion"}}}}, "404": {"description": "Promotion not found"}}}}, "/api/promotion/active": {"get": {"summary": "Get active promotions", "operationId": "GetActivePromotions", "tags": ["Promotion"], "responses": {"200": {"description": "A list of active promotions", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Promotion"}}}}}}}}, "/api/promotionpackage": {"get": {"summary": "Get all promotion packages", "operationId": "GetPromotionPackages", "tags": ["PromotionPackage"], "responses": {"200": {"description": "A list of promotion packages", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PromotionPackage"}}}}}}}}, "/api/promotionpackage/popular": {"get": {"summary": "Get popular promotion packages", "operationId": "GetPopularPromotionPackages", "tags": ["PromotionPackage"], "responses": {"200": {"description": "A list of popular promotion packages", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PromotionPackage"}}}}}}}}, "/api/jobcategory": {"get": {"summary": "Get all job categories", "operationId": "GetJobCategories", "tags": ["JobCategory"], "responses": {"200": {"description": "A list of job categories", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/JobCategory"}}}}}}}}, "/api/organization": {"get": {"summary": "Get all organizations", "operationId": "GetOrganizations", "tags": ["Organization"], "responses": {"200": {"description": "A list of organizations", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Organization"}}}}}}}}, "/api/notification/user/{userId}": {"get": {"summary": "Get user notifications", "operationId": "GetUserNotifications", "tags": ["Notification"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User notifications", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}}}}}}}}, "/api/location": {"get": {"summary": "Get all locations", "operationId": "GetLocations", "tags": ["Location"], "responses": {"200": {"description": "A list of locations", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Location"}}}}}}}}, "/api/skill": {"get": {"summary": "Get all skills", "operationId": "GetSkills", "tags": ["Skill"], "responses": {"200": {"description": "A list of skills", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Skill"}}}}}}}}, "/api/payment/user/{userId}/transactions": {"get": {"summary": "Get user payment transactions", "operationId": "GetUserPaymentTransactions", "tags": ["Payment"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User payment transactions", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentTransaction"}}}}}}}}, "/api/analytics": {"get": {"summary": "Get platform analytics", "operationId": "GetAnalytics", "tags": ["Analytics"], "responses": {"200": {"description": "Platform analytics data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Analytics"}}}}}}}, "/api/referral/user/{userId}": {"get": {"summary": "Get user referrals", "operationId": "GetUserReferrals", "tags": ["Referral"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User referrals", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Referral"}}}}}}}}, "/api/promotion/{id}/analytics": {"get": {"summary": "Get promotion analytics", "operationId": "GetPromotionAnalytics", "tags": ["Promotion"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Promotion analytics data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromotionAnalytics"}}}}, "404": {"description": "Promotion analytics not found"}}}}, "/api/savedsearch/user/{userId}": {"get": {"summary": "Get user saved searches", "operationId": "GetUserSavedSearches", "tags": ["SavedSearch"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User saved searches", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SavedSearch"}}}}}}}, "post": {"summary": "Save a search for user", "operationId": "SaveSearch", "tags": ["SavedSearch"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavedSearch"}}}}, "responses": {"201": {"description": "Search saved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavedSearch"}}}}}}}}, "components": {"schemas": {"TransferRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "requestTitle": {"type": "string"}, "currentLocation": {"$ref": "#/components/schemas/Location"}, "destinationLocation": {"$ref": "#/components/schemas/Location"}, "currentSalaryGrade": {"type": "string"}, "desiredSalaryGrade": {"type": "string"}, "currentFinancialGrade": {"type": "string"}, "desiredFinancialGrade": {"type": "string"}, "industry": {"type": "string"}, "jobCategory": {"type": "string"}, "transferReason": {"type": "string"}, "status": {"type": "string", "enum": ["pending", "approved", "rejected", "expired"]}, "priority": {"type": "string", "enum": ["low", "normal", "high", "urgent"]}, "submissionDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}, "viewCount": {"type": "integer"}, "interestedEmployers": {"type": "integer"}, "documents": {"type": "array", "items": {"type": "string"}}, "requiredSkills": {"type": "array", "items": {"type": "string"}}, "preferredCompanySize": {"type": "string"}, "remoteWorkPreference": {"type": "string"}, "languageRequirements": {"type": "array", "items": {"type": "string"}}, "createdBy": {"$ref": "#/components/schemas/UserProfile"}, "responses": {"type": "array", "items": {"$ref": "#/components/schemas/EmployerResponse"}}}, "required": ["userId", "requestTitle", "currentLocation", "destinationLocation", "industry"]}, "Location": {"type": "object", "properties": {"city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}}}, "UserProfile": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "fullName": {"type": "string"}, "profileImageUrl": {"type": "string"}, "currentPosition": {"type": "string"}, "experience": {"type": "string"}}}, "EmployerResponse": {"type": "object", "properties": {"id": {"type": "string"}, "employerId": {"type": "string"}, "companyName": {"type": "string"}, "responseDate": {"type": "string", "format": "date-time"}, "status": {"type": "string", "enum": ["interested", "interview_scheduled", "offer_made", "rejected"]}, "message": {"type": "string"}, "offeredSalary": {"type": "string"}, "interviewScheduled": {"type": "boolean"}}}, "SubscriptionPlan": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string", "enum": ["bronze", "silver", "gold", "diamond"]}, "displayName": {"type": "string"}, "priceOMR": {"type": "number", "format": "decimal"}, "priceDisplay": {"type": "string"}, "duration": {"type": "string"}, "color": {"type": "string"}, "iconUrl": {"type": "string"}, "maxDestinations": {"type": "integer"}, "priorityListing": {"type": "boolean"}, "priorityFrequency": {"type": "string"}, "whatsAppAlerts": {"type": "boolean"}, "emailAlerts": {"type": "boolean"}, "smsAlerts": {"type": "boolean"}, "inviteLimit": {"type": "integer"}, "inviteLimitDisplay": {"type": "string"}, "tokenMultiplier": {"type": "number", "format": "double"}, "adFreeExperience": {"type": "boolean"}, "features": {"type": "array", "items": {"type": "string"}}, "serviceFeePercentage": {"type": "number", "format": "double"}, "paymentGatewayFeePercentage": {"type": "number", "format": "double"}, "totalFeePercentage": {"type": "number", "format": "double"}, "isPopular": {"type": "boolean"}, "description": {"type": "string"}}, "required": ["id", "name", "priceOMR"]}, "TokenTransaction": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string", "format": "uuid"}, "amount": {"type": "integer"}, "type": {"type": "string", "enum": ["referral", "purchase", "bonus", "redemption", "welcome"]}, "description": {"type": "string"}, "referenceId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "balanceAfter": {"type": "integer"}}, "required": ["userId", "amount", "type", "description"]}, "AddTokensRequest": {"type": "object", "properties": {"amount": {"type": "integer", "minimum": 1}, "type": {"type": "string"}, "description": {"type": "string"}, "referenceId": {"type": "string"}}, "required": ["amount", "type", "description"]}, "DeductTokensRequest": {"type": "object", "properties": {"amount": {"type": "integer", "minimum": 1}, "type": {"type": "string"}, "description": {"type": "string"}, "referenceId": {"type": "string"}}, "required": ["amount", "type", "description"]}, "User": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "firstName": {"type": "string"}, "secondName": {"type": "string"}, "tribe": {"type": "string"}, "fullName": {"type": "string"}, "email": {"type": "string", "format": "email"}, "phoneNumber": {"type": "string"}, "age": {"type": "integer"}, "gender": {"type": "string"}, "currentEmployerLocation": {"type": "string"}, "currentEmployerCity": {"type": "string"}, "currentEmployerState": {"type": "string"}, "currentSalaryGrade": {"type": "string"}, "educationalQualification": {"type": "string"}, "subscriptionTier": {"type": "string"}, "tokenBalance": {"type": "integer"}, "profileImageUrl": {"type": "string"}, "description": {"type": "string"}, "joinDate": {"type": "string", "format": "date-time"}, "isActive": {"type": "boolean"}, "referralCode": {"type": "string"}, "invitedFriends": {"type": "integer"}, "completedTransfers": {"type": "integer"}, "currentEmployer": {"type": "string"}, "currentPosition": {"type": "string"}, "yearsOfExperience": {"type": "integer"}, "skills": {"type": "array", "items": {"type": "string"}}, "languages": {"type": "array", "items": {"type": "string"}}, "certifications": {"type": "array", "items": {"type": "string"}}, "preferredWorkType": {"type": "string", "enum": ["office", "remote", "hybrid"]}, "isAvailableForTransfer": {"type": "boolean"}, "linkedInProfile": {"type": "string"}, "portfolio": {"type": "string"}, "resumeUrl": {"type": "string"}, "bio": {"type": "string"}}, "required": ["email", "fullName"]}, "Promotion": {"type": "object", "properties": {"id": {"type": "string"}, "companyId": {"type": "string"}, "companyName": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string", "enum": ["banner", "sponsored_content", "job_promotion", "directory_listing", "event"]}, "category": {"type": "string"}, "imageUrl": {"type": "string"}, "videoUrl": {"type": "string"}, "websiteUrl": {"type": "string"}, "contactEmail": {"type": "string"}, "contactPhone": {"type": "string"}, "budgetOMR": {"type": "number", "format": "decimal"}, "durationDays": {"type": "integer"}, "targetAudience": {"type": "string"}, "keywords": {"type": "array", "items": {"type": "string"}}, "status": {"type": "string", "enum": ["pending", "approved", "active", "paused", "completed", "rejected"]}, "priority": {"type": "string", "enum": ["low", "normal", "high", "premium"]}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "viewCount": {"type": "integer"}, "clickCount": {"type": "integer"}, "conversionCount": {"type": "integer"}, "totalSpent": {"type": "number", "format": "decimal"}}, "required": ["companyId", "title", "type"]}, "PromotionPackage": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "displayName": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "priceOMR": {"type": "number", "format": "decimal"}, "durationDays": {"type": "integer"}, "maxPromotions": {"type": "integer"}, "maxViews": {"type": "integer"}, "maxClicks": {"type": "integer"}, "features": {"type": "array", "items": {"type": "string"}}, "isPopular": {"type": "boolean"}, "isActive": {"type": "boolean"}}, "required": ["id", "name", "priceOMR"]}, "JobCategory": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "nameArabic": {"type": "string"}, "description": {"type": "string"}, "parentCategoryId": {"type": "string"}, "isActive": {"type": "boolean"}, "sortOrder": {"type": "integer"}, "jobCount": {"type": "integer"}}, "required": ["id", "name"]}, "Organization": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "nameArabic": {"type": "string"}, "type": {"type": "string", "enum": ["government", "private", "semi_government", "international"]}, "sector": {"type": "string"}, "location": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "description": {"type": "string"}, "website": {"type": "string"}, "logoUrl": {"type": "string"}, "employeeCount": {"type": "string"}, "establishedYear": {"type": "integer"}, "isActive": {"type": "boolean"}, "isVerified": {"type": "boolean"}, "contactEmail": {"type": "string"}, "contactPhone": {"type": "string"}}, "required": ["id", "name", "type"]}, "Notification": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string", "format": "uuid"}, "title": {"type": "string"}, "message": {"type": "string"}, "type": {"type": "string", "enum": ["info", "success", "warning", "error", "promotion"]}, "isRead": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "actionUrl": {"type": "string"}, "priority": {"type": "string", "enum": ["low", "normal", "high"]}}, "required": ["id", "userId", "title", "message"]}, "Skill": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "nameArabic": {"type": "string"}, "category": {"type": "string"}, "level": {"type": "string", "enum": ["beginner", "intermediate", "advanced", "expert"]}, "isActive": {"type": "boolean"}, "demandLevel": {"type": "string", "enum": ["low", "medium", "high", "very_high"]}}, "required": ["id", "name"]}, "PaymentTransaction": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string", "format": "uuid"}, "amount": {"type": "number", "format": "decimal"}, "currency": {"type": "string"}, "type": {"type": "string", "enum": ["subscription", "token_purchase", "promotion_package", "premium_feature"]}, "status": {"type": "string", "enum": ["pending", "completed", "failed", "refunded"]}, "paymentMethod": {"type": "string"}, "transactionDate": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "referenceId": {"type": "string"}}, "required": ["id", "userId", "amount", "type"]}, "Analytics": {"type": "object", "properties": {"totalUsers": {"type": "integer"}, "activeUsers": {"type": "integer"}, "totalTransferRequests": {"type": "integer"}, "successfulTransfers": {"type": "integer"}, "totalRevenue": {"type": "number", "format": "decimal"}, "subscriptionRevenue": {"type": "number", "format": "decimal"}, "promotionRevenue": {"type": "number", "format": "decimal"}, "userGrowthRate": {"type": "number", "format": "decimal"}, "averageSessionDuration": {"type": "number", "format": "decimal"}, "topJobCategories": {"type": "array", "items": {"type": "object", "properties": {"category": {"type": "string"}, "count": {"type": "integer"}}}}}}, "Referral": {"type": "object", "properties": {"id": {"type": "string"}, "referrerId": {"type": "string", "format": "uuid"}, "referredUserId": {"type": "string", "format": "uuid"}, "referralCode": {"type": "string"}, "status": {"type": "string", "enum": ["pending", "completed", "rewarded"]}, "rewardAmount": {"type": "number", "format": "decimal"}, "createdAt": {"type": "string", "format": "date-time"}, "completedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "referrerId", "referralCode"]}, "PromotionAnalytics": {"type": "object", "properties": {"id": {"type": "string"}, "promotionId": {"type": "string"}, "date": {"type": "string", "format": "date-time"}, "views": {"type": "integer"}, "clicks": {"type": "integer"}, "conversions": {"type": "integer"}, "impressions": {"type": "integer"}, "uniqueViews": {"type": "integer"}, "bounceRate": {"type": "number", "format": "decimal"}, "averageTimeSpent": {"type": "number", "format": "decimal"}, "costSpent": {"type": "number", "format": "decimal"}, "revenue": {"type": "number", "format": "decimal"}, "deviceTypes": {"type": "object", "properties": {"mobile": {"type": "integer"}, "desktop": {"type": "integer"}, "tablet": {"type": "integer"}}}, "locationData": {"type": "object", "additionalProperties": {"type": "integer"}}, "ageGroups": {"type": "object", "additionalProperties": {"type": "integer"}}, "trafficSources": {"type": "object", "properties": {"direct": {"type": "integer"}, "search": {"type": "integer"}, "social": {"type": "integer"}, "referral": {"type": "integer"}}}}, "required": ["id", "promotionId", "date"]}, "SavedSearch": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string", "format": "uuid"}, "searchName": {"type": "string"}, "searchCriteria": {"type": "object", "properties": {"keywords": {"type": "string"}, "location": {"type": "string"}, "jobCategory": {"type": "string"}, "salaryRange": {"type": "object", "properties": {"min": {"type": "number"}, "max": {"type": "number"}}}, "experienceLevel": {"type": "string"}, "workType": {"type": "string"}}}, "alertsEnabled": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "lastUsed": {"type": "string", "format": "date-time"}}, "required": ["id", "userId", "searchName", "searchCriteria"]}}}}