{"openapi": "3.0.1", "info": {"title": "JobTransfer API", "version": "v1", "description": "API for Omani Job Transfer Application with subscription tiers, token system, and payment processing"}, "servers": [{"url": "https://localhost:5002", "description": "Local development server"}], "paths": {"/api/transferrequest": {"get": {"summary": "Get all transfer requests", "operationId": "GetTransferRequests", "tags": ["TransferRequest"], "responses": {"200": {"description": "A list of transfer requests", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransferRequest"}}}}}}}, "post": {"summary": "Create a new transfer request", "operationId": "CreateTransferRequest", "tags": ["TransferRequest"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequest"}}}}, "responses": {"201": {"description": "Transfer request created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequest"}}}}}}}, "/api/transferrequest/{id}": {"get": {"summary": "Get transfer request by ID", "operationId": "GetTransferRequestById", "tags": ["TransferRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Transfer request found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequest"}}}}, "404": {"description": "Transfer request not found"}}}, "put": {"summary": "Update transfer request", "operationId": "UpdateTransferRequest", "tags": ["TransferRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequest"}}}}, "responses": {"200": {"description": "Transfer request updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequest"}}}}}}, "delete": {"summary": "Delete transfer request", "operationId": "DeleteTransferRequest", "tags": ["TransferRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"204": {"description": "Transfer request deleted successfully"}}}}, "/api/subscription/plans": {"get": {"summary": "Get all subscription plans", "operationId": "GetSubscriptionPlans", "tags": ["Subscription"], "responses": {"200": {"description": "A list of subscription plans", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionPlan"}}}}}}}}, "/api/subscription/plans/{planId}": {"get": {"summary": "Get subscription plan by ID", "operationId": "GetSubscriptionPlan", "tags": ["Subscription"], "parameters": [{"name": "planId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Subscription plan found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionPlan"}}}}, "404": {"description": "Subscription plan not found"}}}}, "/api/token/user/{userId}/transactions": {"get": {"summary": "Get user token transactions", "operationId": "GetUserTokenTransactions", "tags": ["Token"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User token transactions", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TokenTransaction"}}}}}}}}, "/api/token/user/{userId}/balance": {"get": {"summary": "Get user token balance", "operationId": "GetUserTokenBalance", "tags": ["Token"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User token balance", "content": {"application/json": {"schema": {"type": "integer"}}}}}}}, "/api/token/user/{userId}/add": {"post": {"summary": "Add tokens to user account", "operationId": "AddTokens", "tags": ["Token"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddTokensRequest"}}}}, "responses": {"200": {"description": "Tokens added successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenTransaction"}}}}}}}, "/api/token/user/{userId}/deduct": {"post": {"summary": "Deduct tokens from user account", "operationId": "DeductTokens", "tags": ["Token"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeductTokensRequest"}}}}, "responses": {"200": {"description": "Tokens deducted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenTransaction"}}}}, "400": {"description": "Insufficient token balance"}}}}}, "components": {"schemas": {"TransferRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "requestTitle": {"type": "string"}, "currentLocation": {"$ref": "#/components/schemas/Location"}, "destinationLocation": {"$ref": "#/components/schemas/Location"}, "currentSalaryGrade": {"type": "string"}, "desiredSalaryGrade": {"type": "string"}, "currentFinancialGrade": {"type": "string"}, "desiredFinancialGrade": {"type": "string"}, "industry": {"type": "string"}, "jobCategory": {"type": "string"}, "transferReason": {"type": "string"}, "status": {"type": "string", "enum": ["pending", "approved", "rejected", "expired"]}, "priority": {"type": "string", "enum": ["low", "normal", "high", "urgent"]}, "submissionDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}, "viewCount": {"type": "integer"}, "interestedEmployers": {"type": "integer"}, "documents": {"type": "array", "items": {"type": "string"}}, "requiredSkills": {"type": "array", "items": {"type": "string"}}, "preferredCompanySize": {"type": "string"}, "remoteWorkPreference": {"type": "string"}, "languageRequirements": {"type": "array", "items": {"type": "string"}}, "createdBy": {"$ref": "#/components/schemas/UserProfile"}, "responses": {"type": "array", "items": {"$ref": "#/components/schemas/EmployerResponse"}}}, "required": ["userId", "requestTitle", "currentLocation", "destinationLocation", "industry"]}, "Location": {"type": "object", "properties": {"city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}}}, "UserProfile": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "fullName": {"type": "string"}, "profileImageUrl": {"type": "string"}, "currentPosition": {"type": "string"}, "experience": {"type": "string"}}}, "EmployerResponse": {"type": "object", "properties": {"id": {"type": "string"}, "employerId": {"type": "string"}, "companyName": {"type": "string"}, "responseDate": {"type": "string", "format": "date-time"}, "status": {"type": "string", "enum": ["interested", "interview_scheduled", "offer_made", "rejected"]}, "message": {"type": "string"}, "offeredSalary": {"type": "string"}, "interviewScheduled": {"type": "boolean"}}}, "SubscriptionPlan": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string", "enum": ["bronze", "silver", "gold", "diamond"]}, "displayName": {"type": "string"}, "priceOMR": {"type": "number", "format": "decimal"}, "priceDisplay": {"type": "string"}, "duration": {"type": "string"}, "color": {"type": "string"}, "iconUrl": {"type": "string"}, "maxDestinations": {"type": "integer"}, "priorityListing": {"type": "boolean"}, "priorityFrequency": {"type": "string"}, "whatsAppAlerts": {"type": "boolean"}, "emailAlerts": {"type": "boolean"}, "smsAlerts": {"type": "boolean"}, "inviteLimit": {"type": "integer"}, "inviteLimitDisplay": {"type": "string"}, "tokenMultiplier": {"type": "number", "format": "double"}, "adFreeExperience": {"type": "boolean"}, "features": {"type": "array", "items": {"type": "string"}}, "serviceFeePercentage": {"type": "number", "format": "double"}, "paymentGatewayFeePercentage": {"type": "number", "format": "double"}, "totalFeePercentage": {"type": "number", "format": "double"}, "isPopular": {"type": "boolean"}, "description": {"type": "string"}}, "required": ["id", "name", "priceOMR"]}, "TokenTransaction": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string", "format": "uuid"}, "amount": {"type": "integer"}, "type": {"type": "string", "enum": ["referral", "purchase", "bonus", "redemption", "welcome"]}, "description": {"type": "string"}, "referenceId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "balanceAfter": {"type": "integer"}}, "required": ["userId", "amount", "type", "description"]}, "AddTokensRequest": {"type": "object", "properties": {"amount": {"type": "integer", "minimum": 1}, "type": {"type": "string"}, "description": {"type": "string"}, "referenceId": {"type": "string"}}, "required": ["amount", "type", "description"]}, "DeductTokensRequest": {"type": "object", "properties": {"amount": {"type": "integer", "minimum": 1}, "type": {"type": "string"}, "description": {"type": "string"}, "referenceId": {"type": "string"}}, "required": ["amount", "type", "description"]}}}}