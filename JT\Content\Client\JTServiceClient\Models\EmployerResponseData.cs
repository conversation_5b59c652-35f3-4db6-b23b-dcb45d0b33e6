// <auto-generated/>
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System;
namespace JT.Client.Models {
    public class EmployerResponseData : IAdditionalDataHolder, IParsable {
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The companyName property</summary>
        public string CompanyName { get; set; }
        /// <summary>The employerId property</summary>
        public string EmployerId { get; set; }
        /// <summary>The id property</summary>
        public string Id { get; set; }
        /// <summary>The interviewScheduled property</summary>
        public bool? InterviewScheduled { get; set; }
        /// <summary>The message property</summary>
        public string Message { get; set; }
        /// <summary>The offeredSalary property</summary>
        public string OfferedSalary { get; set; }
        /// <summary>The responseDate property</summary>
        public DateTimeOffset? ResponseDate { get; set; }
        /// <summary>The status property</summary>
        public string Status { get; set; }
        /// <summary>
        /// Instantiates a new EmployerResponseData and sets the default values.
        /// </summary>
        public EmployerResponseData() {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static EmployerResponseData CreateFromDiscriminatorValue(IParseNode parseNode) {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new EmployerResponseData();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers() {
            return new Dictionary<string, Action<IParseNode>> {
                {"companyName", n => { CompanyName = n.GetStringValue(); } },
                {"employerId", n => { EmployerId = n.GetStringValue(); } },
                {"id", n => { Id = n.GetStringValue(); } },
                {"interviewScheduled", n => { InterviewScheduled = n.GetBoolValue(); } },
                {"message", n => { Message = n.GetStringValue(); } },
                {"offeredSalary", n => { OfferedSalary = n.GetStringValue(); } },
                {"responseDate", n => { ResponseDate = n.GetDateTimeOffsetValue(); } },
                {"status", n => { Status = n.GetStringValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer) {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("companyName", CompanyName);
            writer.WriteStringValue("employerId", EmployerId);
            writer.WriteStringValue("id", Id);
            writer.WriteBoolValue("interviewScheduled", InterviewScheduled);
            writer.WriteStringValue("message", Message);
            writer.WriteStringValue("offeredSalary", OfferedSalary);
            writer.WriteDateTimeOffsetValue("responseDate", ResponseDate);
            writer.WriteStringValue("status", Status);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
