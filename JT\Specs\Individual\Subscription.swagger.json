{"openapi": "3.0.1", "info": {"title": "Subscription & Token API", "version": "v1", "description": "API for subscription plans and token management in Omani Job Transfer Application"}, "servers": [{"url": "https://localhost:5002", "description": "Local development server"}], "paths": {"/api/subscription/plans": {"get": {"summary": "Get all subscription plans", "operationId": "GetSubscriptionPlans", "tags": ["Subscription"], "responses": {"200": {"description": "A list of subscription plans", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionPlan"}}}}}}}}, "/api/subscription/plans/{planId}": {"get": {"summary": "Get subscription plan by ID", "operationId": "GetSubscriptionPlan", "tags": ["Subscription"], "parameters": [{"name": "planId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Subscription plan found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionPlan"}}}}, "404": {"description": "Subscription plan not found"}}}}, "/api/token/user/{userId}/transactions": {"get": {"summary": "Get user token transactions", "operationId": "GetUserTokenTransactions", "tags": ["Token"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User token transactions", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TokenTransaction"}}}}}}}}, "/api/token/user/{userId}/balance": {"get": {"summary": "Get user token balance", "operationId": "GetUserTokenBalance", "tags": ["Token"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User token balance", "content": {"application/json": {"schema": {"type": "integer"}}}}}}}, "/api/token/user/{userId}/add": {"post": {"summary": "Add tokens to user account", "operationId": "AddTokens", "tags": ["Token"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddTokensRequest"}}}}, "responses": {"200": {"description": "Tokens added successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenTransaction"}}}}}}}, "/api/token/user/{userId}/deduct": {"post": {"summary": "Deduct tokens from user account", "operationId": "DeductTokens", "tags": ["Token"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeductTokensRequest"}}}}, "responses": {"200": {"description": "Tokens deducted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenTransaction"}}}}, "400": {"description": "Insufficient token balance"}}}}}, "components": {"schemas": {"SubscriptionPlan": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "displayName": {"type": "string"}, "description": {"type": "string"}, "priceOMR": {"type": "number", "format": "decimal"}, "billingCycle": {"type": "string", "enum": ["monthly", "quarterly", "yearly"]}, "features": {"type": "array", "items": {"type": "string"}}, "maxTransferRequests": {"type": "integer"}, "maxPromotions": {"type": "integer"}, "tokensIncluded": {"type": "integer"}, "prioritySupport": {"type": "boolean"}, "analyticsAccess": {"type": "boolean"}, "isPopular": {"type": "boolean"}, "isActive": {"type": "boolean"}}, "required": ["id", "name", "priceOMR"]}, "TokenTransaction": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string", "format": "uuid"}, "amount": {"type": "integer"}, "type": {"type": "string", "enum": ["earned", "spent", "purchased", "bonus", "refund"]}, "description": {"type": "string"}, "transactionDate": {"type": "string", "format": "date-time"}, "balanceAfter": {"type": "integer"}, "source": {"type": "string"}, "referenceId": {"type": "string"}}, "required": ["id", "userId", "amount", "type"]}, "AddTokensRequest": {"type": "object", "properties": {"amount": {"type": "integer"}, "type": {"type": "string", "enum": ["earned", "purchased", "bonus"]}, "description": {"type": "string"}, "source": {"type": "string"}}, "required": ["amount", "type", "description"]}, "DeductTokensRequest": {"type": "object", "properties": {"amount": {"type": "integer"}, "type": {"type": "string", "enum": ["spent"]}, "description": {"type": "string"}, "source": {"type": "string"}}, "required": ["amount", "type", "description"]}}}}