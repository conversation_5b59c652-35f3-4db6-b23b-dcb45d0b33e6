{"openapi": "3.0.1", "info": {"title": "Promotion API", "version": "v1", "description": "API for commercial promotion system in Omani Job Transfer Application"}, "servers": [{"url": "https://localhost:5002", "description": "Local development server"}], "paths": {"/api/promotion": {"get": {"summary": "Get all promotions", "operationId": "GetPromotions", "tags": ["Promotion"], "responses": {"200": {"description": "A list of promotions", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Promotion"}}}}}}}, "post": {"summary": "Create a new promotion", "operationId": "CreatePromotion", "tags": ["Promotion"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Promotion"}}}}, "responses": {"201": {"description": "Promotion created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Promotion"}}}}}}}, "/api/promotion/{id}": {"get": {"summary": "Get promotion by ID", "operationId": "GetPromotionById", "tags": ["Promotion"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Promotion found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Promotion"}}}}, "404": {"description": "Promotion not found"}}}}, "/api/promotion/active": {"get": {"summary": "Get active promotions", "operationId": "GetActivePromotions", "tags": ["Promotion"], "responses": {"200": {"description": "A list of active promotions", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Promotion"}}}}}}}}, "/api/promotion/{id}/analytics": {"get": {"summary": "Get promotion analytics", "operationId": "GetPromotionAnalytics", "tags": ["Promotion"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Promotion analytics data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromotionAnalytics"}}}}, "404": {"description": "Promotion analytics not found"}}}}, "/api/promotionpackage": {"get": {"summary": "Get all promotion packages", "operationId": "GetPromotionPackages", "tags": ["PromotionPackage"], "responses": {"200": {"description": "A list of promotion packages", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PromotionPackage"}}}}}}}}, "/api/promotionpackage/popular": {"get": {"summary": "Get popular promotion packages", "operationId": "GetPopularPromotionPackages", "tags": ["PromotionPackage"], "responses": {"200": {"description": "A list of popular promotion packages", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PromotionPackage"}}}}}}}}}, "components": {"schemas": {"Promotion": {"type": "object", "properties": {"id": {"type": "string"}, "companyId": {"type": "string"}, "companyName": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string", "enum": ["banner", "sponsored_content", "job_promotion", "directory_listing", "event"]}, "category": {"type": "string"}, "imageUrl": {"type": "string"}, "videoUrl": {"type": "string"}, "websiteUrl": {"type": "string"}, "contactEmail": {"type": "string"}, "contactPhone": {"type": "string"}, "budgetOMR": {"type": "number", "format": "decimal"}, "durationDays": {"type": "integer"}, "targetAudience": {"type": "string"}, "keywords": {"type": "array", "items": {"type": "string"}}, "status": {"type": "string", "enum": ["pending", "approved", "active", "paused", "completed", "rejected"]}, "priority": {"type": "string", "enum": ["low", "normal", "high", "premium"]}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "viewCount": {"type": "integer"}, "clickCount": {"type": "integer"}, "conversionCount": {"type": "integer"}, "totalSpent": {"type": "number", "format": "decimal"}}, "required": ["companyId", "title", "type"]}, "PromotionPackage": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "displayName": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "priceOMR": {"type": "number", "format": "decimal"}, "durationDays": {"type": "integer"}, "maxPromotions": {"type": "integer"}, "maxViews": {"type": "integer"}, "maxClicks": {"type": "integer"}, "features": {"type": "array", "items": {"type": "string"}}, "isPopular": {"type": "boolean"}, "isActive": {"type": "boolean"}}, "required": ["id", "name", "priceOMR"]}, "PromotionAnalytics": {"type": "object", "properties": {"id": {"type": "string"}, "promotionId": {"type": "string"}, "date": {"type": "string", "format": "date-time"}, "views": {"type": "integer"}, "clicks": {"type": "integer"}, "conversions": {"type": "integer"}, "impressions": {"type": "integer"}, "uniqueViews": {"type": "integer"}, "bounceRate": {"type": "number", "format": "decimal"}, "averageTimeSpent": {"type": "number", "format": "decimal"}, "costSpent": {"type": "number", "format": "decimal"}, "revenue": {"type": "number", "format": "decimal"}, "deviceTypes": {"type": "object", "properties": {"mobile": {"type": "integer"}, "desktop": {"type": "integer"}, "tablet": {"type": "integer"}}}, "locationData": {"type": "object", "additionalProperties": {"type": "integer"}}, "ageGroups": {"type": "object", "additionalProperties": {"type": "integer"}}, "trafficSources": {"type": "object", "properties": {"direct": {"type": "integer"}, "search": {"type": "integer"}, "social": {"type": "integer"}, "referral": {"type": "integer"}}}}, "required": ["id", "promotionId", "date"]}}}}