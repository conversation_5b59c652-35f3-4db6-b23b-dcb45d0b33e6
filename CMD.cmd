dotnet tool install --global Microsoft.OpenApi.Kiota

# If using a URL for the spec:
kiota generate --openapi https://api.example.com/products/swagger.json --language CSharp --class-name ProductServiceClient --namespace-name Meo.Client.Products --output d:\source\Meo\Meo\Content\Client\ProductClient

# Or, if using a local file:
kiota generate --openapi D:\specs\ProductService.swagger.json --language CSharp --class-name ProductServiceClient --namespace-name Meo.Client.Products --output d:\source\Meo\Meo\Content\Client\ProductClient

// this is the one that works
kiota generate --openapi D:\source\Meo\Meo\Specs\ProductService.swagger.json --language CSharp --class-name ProductServiceClient --namespace-name Meo.Client.Products --output d:\source\Meo\Meo\Content\Client\ProductClient       

// this is the one that works
kiota generate --openapi D:\source\Meo\Meo\Specs\UsersService.swagger
.json --language CSharp --class-name UsersServiceClient --namespace-name Meo.Services.Endpo
ints.UsersService --output d:\source\Meo\Meo\Services\Endpoints\UsersService  


kiota generate --openapi Meo\Specs\ProductService.swagger.json --output-path Meo\Content\Client\ProductServiceClient --language CSharp --namespace-name Meo.Client --client-class-name ProductServiceClient

dotnet kiota generate -d .\Specs\UsersService.swagger.json -o .\Services\Endpoints\UsersService -c UsersServiceClient -n Meo.Services.Endpoints.UsersService