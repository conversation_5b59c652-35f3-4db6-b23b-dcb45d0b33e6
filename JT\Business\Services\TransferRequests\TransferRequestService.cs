using JT.Client.Api;

namespace JT.Business.Services.TransferRequests;

public class TransferRequestService : ITransferRequestService
{
	private readonly JTServiceClient api;

	public TransferRequestService(JTServiceClient api)
	{
		this.api = api;
	}

	public async ValueTask<IImmutableList<TransferRequest>> GetAll(CancellationToken ct = default)
	{
		var transferRequestsData = await api.Api.TransferRequest.GetAsync(cancellationToken: ct);
		return transferRequestsData?.Select(r => new TransferRequest(r)).ToImmutableList() ?? ImmutableList<TransferRequest>.Empty;
	}

	public async ValueTask<IImmutableList<TransferRequest>> GetByUser(Guid userId, CancellationToken ct = default)
	{
		var allRequests = await GetAll(ct);
		return allRequests.Where(r => r.UserId == userId).ToImmutableList();
	}

	public async ValueTask<TransferRequest?> GetById(Guid id, CancellationToken ct = default)
	{
		try
		{
			var transferRequestData = await api.Api.TransferRequest[id.ToString()].GetAsync(cancellationToken: ct);
			return transferRequestData != null ? new TransferRequest(transferRequestData) : null;
		}
		catch
		{
			return null;
		}
	}

	public async ValueTask<IImmutableList<TransferRequest>> GetByStatus(string status, CancellationToken ct = default)
	{
		var allRequests = await GetAll(ct);
		return allRequests.Where(r => string.Equals(r.Status, status, StringComparison.OrdinalIgnoreCase)).ToImmutableList();
	}

	public async ValueTask<IImmutableList<TransferRequest>> GetByLocation(string location, CancellationToken ct = default)
	{
		var allRequests = await GetAll(ct);
		return allRequests.Where(r => 
			r.DestinationLocation.City?.Contains(location, StringComparison.OrdinalIgnoreCase) == true ||
			r.DestinationLocation.State?.Contains(location, StringComparison.OrdinalIgnoreCase) == true ||
			r.DestinationLocation.Country?.Contains(location, StringComparison.OrdinalIgnoreCase) == true
		).ToImmutableList();
	}

	public async ValueTask<IImmutableList<TransferRequest>> GetByIndustry(string industry, CancellationToken ct = default)
	{
		var allRequests = await GetAll(ct);
		return allRequests.Where(r => string.Equals(r.Industry, industry, StringComparison.OrdinalIgnoreCase)).ToImmutableList();
	}

	public async ValueTask<IImmutableList<TransferRequest>> Search(string query, CancellationToken ct = default)
	{
		var allRequests = await GetAll(ct);
		return allRequests.Where(r => 
			r.RequestTitle?.Contains(query, StringComparison.OrdinalIgnoreCase) == true ||
			r.Industry?.Contains(query, StringComparison.OrdinalIgnoreCase) == true ||
			r.JobCategory?.Contains(query, StringComparison.OrdinalIgnoreCase) == true ||
			r.RequiredSkills.Any(skill => skill.Contains(query, StringComparison.OrdinalIgnoreCase))
		).ToImmutableList();
	}

	public async ValueTask<TransferRequest> Create(TransferRequest transferRequest, CancellationToken ct = default)
	{
		var transferRequestData = transferRequest.ToData();
		var createdData = await api.Api.TransferRequest.PostAsync(transferRequestData, cancellationToken: ct);
		return new TransferRequest(createdData);
	}

	public async ValueTask<TransferRequest> Update(TransferRequest transferRequest, CancellationToken ct = default)
	{
		var transferRequestData = transferRequest.ToData();
		var updatedData = await api.Api.TransferRequest[transferRequest.Id.ToString()].PutAsync(transferRequestData, cancellationToken: ct);
		return new TransferRequest(updatedData);
	}

	public async ValueTask<bool> Delete(Guid id, CancellationToken ct = default)
	{
		try
		{
			await api.Api.TransferRequest[id.ToString()].DeleteAsync(cancellationToken: ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<bool> UpdateStatus(Guid id, string status, CancellationToken ct = default)
	{
		try
		{
			var transferRequest = await GetById(id, ct);
			if (transferRequest == null) return false;

			var updatedRequest = transferRequest with { Status = status };
			await Update(updatedRequest, ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<bool> IncrementViewCount(Guid id, CancellationToken ct = default)
	{
		try
		{
			var transferRequest = await GetById(id, ct);
			if (transferRequest == null) return false;

			var updatedRequest = transferRequest with { ViewCount = transferRequest.ViewCount + 1 };
			await Update(updatedRequest, ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<IImmutableList<TransferRequest>> GetTrending(CancellationToken ct = default)
	{
		var allRequests = await GetAll(ct);
		return allRequests
			.Where(r => r.IsActive)
			.OrderByDescending(r => r.ViewCount)
			.ThenByDescending(r => r.InterestedEmployers)
			.Take(10)
			.ToImmutableList();
	}

	public async ValueTask<IImmutableList<TransferRequest>> GetExpiringSoon(CancellationToken ct = default)
	{
		var allRequests = await GetAll(ct);
		return allRequests
			.Where(r => r.IsActive && r.DaysUntilExpiration <= 7)
			.OrderBy(r => r.DaysUntilExpiration)
			.ToImmutableList();
	}
}
