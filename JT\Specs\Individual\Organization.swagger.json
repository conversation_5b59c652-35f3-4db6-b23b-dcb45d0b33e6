{"openapi": "3.0.1", "info": {"title": "Organization & Master Data API", "version": "v1", "description": "API for organizations, job categories, locations, and skills in Omani Job Transfer Application"}, "servers": [{"url": "https://localhost:5002", "description": "Local development server"}], "paths": {"/api/organization": {"get": {"summary": "Get all organizations", "operationId": "GetOrganizations", "tags": ["Organization"], "responses": {"200": {"description": "A list of organizations", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Organization"}}}}}}}}, "/api/jobcategory": {"get": {"summary": "Get all job categories", "operationId": "GetJobCategories", "tags": ["JobCategory"], "responses": {"200": {"description": "A list of job categories", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/JobCategory"}}}}}}}}, "/api/location": {"get": {"summary": "Get all locations", "operationId": "GetLocations", "tags": ["Location"], "responses": {"200": {"description": "A list of locations", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Location"}}}}}}}}, "/api/skill": {"get": {"summary": "Get all skills", "operationId": "GetSkills", "tags": ["Skill"], "responses": {"200": {"description": "A list of skills", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Skill"}}}}}}}}}, "components": {"schemas": {"Organization": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "nameArabic": {"type": "string"}, "type": {"type": "string", "enum": ["government", "private", "semi_government", "international"]}, "sector": {"type": "string"}, "location": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "description": {"type": "string"}, "website": {"type": "string"}, "logoUrl": {"type": "string"}, "employeeCount": {"type": "string"}, "establishedYear": {"type": "integer"}, "isActive": {"type": "boolean"}, "isVerified": {"type": "boolean"}, "contactEmail": {"type": "string"}, "contactPhone": {"type": "string"}}, "required": ["id", "name", "type"]}, "JobCategory": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "nameArabic": {"type": "string"}, "description": {"type": "string"}, "parentCategoryId": {"type": "string"}, "isActive": {"type": "boolean"}, "sortOrder": {"type": "integer"}, "jobCount": {"type": "integer"}}, "required": ["id", "name"]}, "Location": {"type": "object", "properties": {"id": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}, "region": {"type": "string"}, "isActive": {"type": "boolean"}, "population": {"type": "integer"}, "economicZone": {"type": "string"}}, "required": ["id", "city", "country"]}, "Skill": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "nameArabic": {"type": "string"}, "category": {"type": "string"}, "level": {"type": "string", "enum": ["beginner", "intermediate", "advanced", "expert"]}, "isActive": {"type": "boolean"}, "demandLevel": {"type": "string", "enum": ["low", "medium", "high", "very_high"]}}, "required": ["id", "name"]}}}}