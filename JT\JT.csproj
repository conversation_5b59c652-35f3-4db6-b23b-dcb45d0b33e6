<Project Sdk="Uno.Sdk">
  <PropertyGroup>
    <!-- Building with dotnet build -f net9.0-platform will still cause restore to happen for all TargetFrameworks -->
    <!-- which will force us to install all workloads even if not needed -->
    <!-- To prevent that, we will pass TargetFrameworkOverride as a global property (i.e, dotnet build -p:TargetFrameworkOverride=net9.0-platform) -->
    <!-- That way, we set TargetFrameworks property to only the needed TargetFramework -->
    <TargetFrameworks Condition="'$(TargetFrameworkOverride)'!=''">$(TargetFrameworkOverride)</TargetFrameworks>
    <TargetFrameworks Condition="'$(TargetFrameworkOverride)'==''">
    net9.0-android;net9.0-ios;net9.0-windows10.0.26100;net9.0-browserwasm;net9.0-desktop;net9.0</TargetFrameworks>

    <OutputType>Exe</OutputType>
    <UnoSingleProject>true</UnoSingleProject>

    <!-- Display name -->
    <ApplicationTitle>JT</ApplicationTitle>
    <!-- App Identifier -->
    <ApplicationId>com.companyname.JT</ApplicationId>
    <ApplicationId Condition="'$(IsCanaryBranch)'=='true'">$(ApplicationId)-canary</ApplicationId>
      <!-- Versions -->
    <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
    <ApplicationVersion>1</ApplicationVersion>
    <UseMocks Condition="'$(UseMocks)'==''">true</UseMocks>
    <!-- Package Publisher -->
    <ApplicationPublisher>MOE</ApplicationPublisher>
    <!-- Package Description -->
    <Description>JT powered by Uno Platform.</Description>
    <!--
      If you encounter this error message:

        error NETSDK1148: A referenced assembly was compiled using a newer version of Microsoft.Windows.SDK.NET.dll.
        Please update to a newer .NET SDK in order to reference this assembly.

      This means that the two packages below must be aligned with the "build" version number of
      the "Microsoft.Windows.SDK.BuildTools" package above, and the "revision" version number
      must be the highest found in https://www.nuget.org/packages/Microsoft.Windows.SDK.NET.Ref.
    -->
    <!-- <WindowsSdkPackageVersion>10.0.22621.28</WindowsSdkPackageVersion> -->

    <!--
      UnoFeatures let's you quickly add and manage implicit package references based on the features you want to use.
      https://aka.platform.uno/singleproject-features
    -->
    <UnoFeatures>
      Material;
      Dsp;
      Hosting;
      Toolkit;
      Logging;
      MVUX;
      Configuration;
      HttpKiota;
      Serialization;
      Localization;
      AuthenticationOidc;
      Navigation;
      Skia;
      ThemeService;
      SkiaRenderer;
    </UnoFeatures>
      <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)'=='Debug' or '$(IsUiAutomationMappingEnabled)'=='True'">
    <IsUiAutomationMappingEnabled>True</IsUiAutomationMappingEnabled>
    <DefineConstants>$(DefineConstants);USE_UITESTS</DefineConstants>
  </PropertyGroup>

    <PropertyGroup Condition="'$(UseMocks)'=='true'">
        <DefineConstants>$(DefineConstants);USE_MOCKS</DefineConstants>
    </PropertyGroup>
    
  <ItemGroup>
    <ProjectReference Include="..\JT.DataContracts\JT.DataContracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Presentation\ShellControl.xaml.cs">
      <DependentUpon>ShellControl.xaml</DependentUpon>
    </Compile>
  </ItemGroup>

    <ItemGroup>
        <Content Include="..\AppData\*.json" LinkBase="AppData">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
    </ItemGroup>

    <ItemGroup>
      <None Remove="Presentation\Controls\ChartControl.xaml" />
    </ItemGroup>

    <ItemGroup>
      <Page Update="Presentation\HomePage.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Presentation\Flyouts\ResponsiveDrawerFlyout.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\Button.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\ChartBrushes.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\CustomFonts.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\FeedView.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\Images.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\MaterialFontsOverride.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\NavigationBar.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\Page.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\Strings.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\TextBox.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Presentation\WelcomePage.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Presentation\Controls\ChartControl.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Presentation\Controls\WelcomeView.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="LiveChartsCore.SkiaSharpView.Uno.WinUI" />
    </ItemGroup>

    <ItemGroup>
      <Page Update="Converters\Converters.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Presentation\Templates\ItemTemplates.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>
</Project>
