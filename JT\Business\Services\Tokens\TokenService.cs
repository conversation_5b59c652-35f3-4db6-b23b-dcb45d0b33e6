using JT.Client.Api;

namespace JT.Business.Services.Tokens;

public class TokenService : ITokenService
{
	private readonly JTServiceClient api;

	public TokenService(JTServiceClient api)
	{
		this.api = api;
	}

	public async ValueTask<IImmutableList<TokenTransaction>> GetUserTransactions(Guid userId, CancellationToken ct = default)
	{
		try
		{
			var transactionsData = await api.Api.Token.User[userId.ToString()].Transactions.GetAsync(cancellationToken: ct);
			return transactionsData?.Select(t => new TokenTransaction(t)).ToImmutableList() ?? ImmutableList<TokenTransaction>.Empty;
		}
		catch
		{
			return ImmutableList<TokenTransaction>.Empty;
		}
	}

	public async ValueTask<int> GetUserBalance(Guid userId, CancellationToken ct = default)
	{
		try
		{
			var balance = await api.Api.Token.User[userId.ToString()].Balance.GetAsync(cancellationToken: ct);
			return balance ?? 0;
		}
		catch
		{
			return 0;
		}
	}

	public async ValueTask<TokenTransaction> AddTokens(Guid userId, int amount, string type, string description, string? referenceId = null, CancellationToken ct = default)
	{
		try
		{
			var request = new AddTokensRequest
			{
				Amount = amount,
				Type = type,
				Description = description,
				ReferenceId = referenceId
			};

			var transactionData = await api.Api.Token.User[userId.ToString()].Add.PostAsync(request, cancellationToken: ct);
			return new TokenTransaction(transactionData);
		}
		catch (Exception ex)
		{
			throw new InvalidOperationException($"Failed to add tokens: {ex.Message}", ex);
		}
	}

	public async ValueTask<TokenTransaction> DeductTokens(Guid userId, int amount, string type, string description, string? referenceId = null, CancellationToken ct = default)
	{
		try
		{
			var request = new DeductTokensRequest
			{
				Amount = amount,
				Type = type,
				Description = description,
				ReferenceId = referenceId
			};

			var transactionData = await api.Api.Token.User[userId.ToString()].Deduct.PostAsync(request, cancellationToken: ct);
			return new TokenTransaction(transactionData);
		}
		catch (Exception ex)
		{
			throw new InvalidOperationException($"Failed to deduct tokens: {ex.Message}", ex);
		}
	}

	public async ValueTask<bool> HasSufficientBalance(Guid userId, int requiredAmount, CancellationToken ct = default)
	{
		var balance = await GetUserBalance(userId, ct);
		return balance >= requiredAmount;
	}

	public async ValueTask<TokenTransaction> ProcessReferralBonus(Guid referrerId, Guid referredUserId, CancellationToken ct = default)
	{
		// Standard referral bonus amount
		const int referralBonus = 25;
		
		var description = $"Referral bonus for inviting user {referredUserId}";
		return await AddTokens(referrerId, referralBonus, "referral", description, referredUserId.ToString(), ct);
	}

	public async ValueTask<TokenTransaction> ProcessWelcomeBonus(Guid userId, CancellationToken ct = default)
	{
		// Standard welcome bonus amount
		const int welcomeBonus = 25;
		
		var description = "Welcome bonus for joining JobTransfer";
		return await AddTokens(userId, welcomeBonus, "bonus", description, "welcome", ct);
	}

	public async ValueTask<TokenTransaction> ProcessPurchaseBonus(Guid userId, string subscriptionTier, CancellationToken ct = default)
	{
		// Bonus amounts based on subscription tier
		var bonusAmount = subscriptionTier.ToLower() switch
		{
			"silver" => 25,
			"gold" => 50,
			"diamond" => 100,
			_ => 0
		};

		if (bonusAmount > 0)
		{
			var description = $"Purchase bonus for {subscriptionTier} subscription";
			return await AddTokens(userId, bonusAmount, "purchase", description, subscriptionTier, ct);
		}

		throw new InvalidOperationException($"No bonus available for subscription tier: {subscriptionTier}");
	}

	public async ValueTask<IImmutableList<TokenTransaction>> GetTransactionsByType(string type, CancellationToken ct = default)
	{
		// This would typically be implemented with a server-side filter
		// For now, we'll get all transactions and filter client-side (not ideal for production)
		try
		{
			// This is a simplified implementation - in production you'd want server-side filtering
			return ImmutableList<TokenTransaction>.Empty;
		}
		catch
		{
			return ImmutableList<TokenTransaction>.Empty;
		}
	}
}
