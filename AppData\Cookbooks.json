[{"Id": "9a16d616-c7a2-4517-966b-a8133141d8fb", "Name": "Breakfast", "UserId": "3c896419-e280-40e7-8552-240635566fed", "PinsNumber": 6, "Recipes": [{"Name": "Fresh Salad Thaid", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "0007e65f-3d5c-4ca3-842f-134b7f4a8b52", "Steps": [{"Name": "Getting started", "CookTime": "00:03:00", "Cookware": ["Knife", "Dish"], "Description": "Cut all your vegetables to size.\n\nYou can also use a bag of pre-shredded coleslaw.\n\nI diced the English cucumbers and cut the red bell peppers into thin strips.\n\nYou may also use julienne carrots, lettuce, etc.", "Ingredients": ["Carrot"], "Number": 1, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:06:00", "Cookware": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Knife", "Cutting Boards"], "Description": "Place the dressing ingredients in a jar and shake together until well combined.\n\nAdd the mix salad greens first, then strategically place the carrot, onion, red pepper and cucumber around the mixed greens.", "Ingredients": ["<PERSON><PERSON>", "Carrot", "Onion", "Red Pepper", "<PERSON><PERSON><PERSON>ber"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:01:00", "Cookware": ["Fork", "Knife", "Bowl"], "Description": "Finish up with the dressing.\n\nAdd the dressing just before you want to serve the salad otherwise it'll go soggy.", "Ingredients": ["Lime Juice", "Sesame Oil", "<PERSON>"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/fresh_salad_thaid.png", "Serves": 1, "CookTime": "00:10:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/lemon.png", "Name": "Lime Juice", "Quantity": "80 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/cucumber.png", "Name": "<PERSON><PERSON><PERSON>ber", "Quantity": "50 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/carrot.png", "Name": "Carrot", "Quantity": "50 g"}], "Calories": "350 kcal", "Reviews": [{"Id": "bd95eeef-643e-4eb0-bbd8-1456791040f8", "RecipeId": "0007e65f-3d5c-4ca3-842f-134b7f4a8b52", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}, {"Id": "6f597075-773c-4160-bf75-77054fee55cf", "RecipeId": "0007e65f-3d5c-4ca3-842f-134b7f4a8b52", "PublisherName": "<PERSON>", "UrlAuthorImage": "ms-appx:///Assets/Profiles/james_wolden.png", "CreatedBy": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Fresh, healthy and tasty", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 2, "UrlIcon": "ms-appx:///Assets/Icons/hamburger.png", "Name": "Lunch", "Color": "#507FF7"}, "Date": "2022-10-18T00:00:00Z", "Save": false}, {"Name": "Salmon <PERSON>", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "5ad60d30-2891-4c4b-99a7-e138e6477191", "Steps": [{"Name": "Getting started", "CookTime": "00:04:00", "Cookware": ["Knife", "Dish", "<PERSON><PERSON><PERSON>"], "Description": "Whenever I have fresh spinach in the house, I love wilting it into the sauce during the final few minutes of cooking to add some greens to this dish.", "Ingredients": ["<PERSON><PERSON>"], "Number": 1, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:02:00", "Cookware": ["Knife", "Dish", "<PERSON><PERSON><PERSON>"], "Description": "If you happy to have any other Italian herbs in the house (rosemary, oregano, thyme, etc), feel free to add them in to taste", "Ingredients": ["<PERSON>", "Oregano"], "Number": 2, "UrlVideo": ""}, {"Name": "Third step", "CookTime": "00:04:00", "Cookware": ["<PERSON>poon", "Dish", "Knife"], "Description": "Instead of (or — if you’re feeling extra-indulgent — in addition to) the heavy cream, add in a torn ball of fresh burrata to the dish just before serving.\n\nI can vouch that it is heavenly.", "Ingredients": ["Cream", "Fresh burrata"], "Number": 3, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:01:00", "Cookware": ["Wooden spoon"], "Description": "I have to admit that I really prefer the texture/flavor of this dish using fresh tomatoes.\n\nBut if you’re in a pinch, it will also taste great using one large (28-ounce) can of good-quality whole tomatoes, which you can break up with a wooden spoon as they cook.", "Ingredients": ["Tomatoes", "Sesame Oil", "<PERSON>"], "Number": 4, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/salmon_tomato_sauce.png", "Serves": 1, "CookTime": "00:11:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/leafy_green.png", "Name": "<PERSON><PERSON>", "Quantity": "200 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/hot_pepper.png", "Name": "<PERSON>", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/tomato.png", "Name": "Tomatoes", "Quantity": "100 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/butter.png", "Name": "Fresh burrata", "Quantity": "50 g"}], "Calories": "200 kcal", "Reviews": [{"Id": "9702c946-c6f0-4f18-91f0-a959990bb306", "RecipeId": "5ad60d30-2891-4c4b-99a7-e138e6477191", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}, {"Id": "2689c8d7-fd91-4495-95e3-9a11e6085473", "RecipeId": "5ad60d30-2891-4c4b-99a7-e138e6477191", "PublisherName": "<PERSON>", "UrlAuthorImage": "ms-appx:///Assets/Profiles/james_wolden.png", "CreatedBy": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Nutritious and great after workout.", "Creator": {"Id": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "UrlProfileImage": "ms-appx:///Assets/Profiles/troyan_smith.png", "FullName": "<PERSON><PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 147, "Following": 259, "Recipes": 45}, "Category": {"Id": 2, "UrlIcon": "ms-appx:///Assets/Icons/hamburger.png", "Name": "Lunch", "Color": "#507FF7"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Fresh Salad Pasta", "UserId": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Id": "b9e6502b-b373-4fcf-86b2-89379667aa5c", "Steps": [{"Name": "Getting started", "CookTime": "00:12:00", "Cookware": ["Pot"], "Description": "Bring a large pot of lightly salted water to a boil.\n\nCook pasta in the boiling water, stirring occasionally, until tender yet firm to the bite, about 10 to 12 minutes; rinse under cold water and drain.", "Ingredients": ["Pasta"], "Number": 1, "UrlVideo": ""}, {"Name": "Next step", "CookTime": "00:02:00", "Cookware": ["<PERSON><PERSON>"], "Description": "Whisk Italian dressing and salad spice mix together until smooth", "Ingredients": ["Spices"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:01:00", "Cookware": ["Salad bowl"], "Description": "Combine pasta, tomatoes, bell peppers, and olives in a salad bowl; pour dressing over salad and toss to coat.\n\nRefrigerate salad, 8 hours to overnight.", "Ingredients": ["Tomatoes", "Pasta", "Bell Peppers", "Olives"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/fresh_salad_pasta.png", "Serves": 1, "CookTime": "00:25:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt and peppers", "Quantity": "30 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/tomato.png", "Name": "Tomatoes", "Quantity": "100 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/olive.png", "Name": "Olives", "Quantity": "50 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/spaghetti.png", "Name": "Pasta", "Quantity": "50 g"}], "Calories": "350 kcal", "Reviews": [{"Id": "6a0fa592-1b1d-4855-ba83-7f6fd0120830", "RecipeId": "b9e6502b-b373-4fcf-86b2-89379667aa5c", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}, {"Id": "60d799c0-804e-4f48-9ad0-5a4def0bcbd2", "RecipeId": "b9e6502b-b373-4fcf-86b2-89379667aa5c", "PublisherName": "<PERSON>", "UrlAuthorImage": "ms-appx:///Assets/Profiles/james_wolden.png", "CreatedBy": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Healthy and tasty", "Creator": {"Id": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "UrlProfileImage": "ms-appx:///Assets/Profiles/troyan_smith.png", "FullName": "<PERSON><PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 147, "Following": 259, "Recipes": 45}, "Category": {"Id": 3, "UrlIcon": "ms-appx:///Assets/Icons/fork_and_knife_with_plate.png", "Name": "Dinner", "Color": "#F16583"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Circle Cake", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "1dc0e330-ab16-43cc-9b84-fdaf2c5e83cd", "Steps": [{"Name": "Getting started", "CookTime": "00:02:00", "Cookware": ["Bowl", "<PERSON>poon", "<PERSON><PERSON>", "Oven"], "Description": "Position a rack in the middle of the oven and preheat to 350 degrees.\n\nWhile the oven is heating, combine the flour, baking powder, and salt in a bowl, mixing well.", "Ingredients": ["Flour", "Baking powder", "Salt"], "Number": 1, "UrlVideo": ""}, {"Name": "First step", "CookTime": "00:05:00", "Cookware": ["Bowl", "Mixer"], "Description": "Place the butter and sugar in the bowl of a heavy-duty mixer fitted with the paddle attachment and beat on medium speed for about 5 minutes, or until very soft and light.\n\nBeat in the vanilla.", "Ingredients": ["Butter", "Sugar", "Vanilla"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:30:00", "Cookware": ["Toothpick", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "Description": "Bake the layers for about 30 to 35 minutes, until they are well risen and firm and a toothpick inserted in the center emerges clean.\n\nCool the layers in the pans on racks for 5 minutes, then unmold onto racks to finish cooling right side up.", "Ingredients": ["Butter"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/circle_cake.png", "Serves": 1, "CookTime": "00:37:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/butter.png", "Name": "Butter", "Quantity": "500 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "50 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/flour.png", "Name": "Flour", "Quantity": "1000 gr"}], "Calories": "550 kcal", "Reviews": [{"Id": "9de1691e-da43-4788-8a98-0e5b2f2a2a6c", "RecipeId": "1dc0e330-ab16-43cc-9b84-fdaf2c5e83cd", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}, {"Id": "8a8e8398-049f-466c-8f7e-634c30815cd8", "RecipeId": "1dc0e330-ab16-43cc-9b84-fdaf2c5e83cd", "PublisherName": "<PERSON>", "UrlAuthorImage": "ms-appx:///Assets/Profiles/james_wolden.png", "CreatedBy": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Impressive with a very fancy form", "Creator": {"Id": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "UrlProfileImage": "ms-appx:///Assets/Profiles/troyan_smith.png", "FullName": "<PERSON><PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 147, "Following": 259, "Recipes": 45}, "Category": {"Id": 4, "UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Snack", "Color": "#CAC2FC"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Mom's Cheesecake", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "f89a5305-4d77-4f66-a97c-65adba6b36ff", "Steps": [{"Name": "Getting started", "CookTime": "00:11:00", "Cookware": ["<PERSON><PERSON>", "Bowl"], "Description": "In the bowl of a stand mixer or in a large bowl (using a hand mixer) add cream cheese and stir until smooth and creamy (don’t over-beat or you’ll incorporate too much air).\n\nAdd sugar and stir again until creamy.", "Ingredients": ["Sugar", "Cream Cheese"], "Number": 1, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:01:00", "Cookware": ["<PERSON><PERSON>", "Spa<PERSON>la"], "Description": "Add sour cream, vanilla extract, and salt, and stir until well-combined.\n\nIf using a stand mixer, make sure you pause periodically to scrape the sides and bottom of the bowl with a spatula so that all ingredients are evenly incorporated", "Ingredients": ["Sour cream, Vanilla extract"], "Number": 3, "UrlVideo": ""}, {"Name": "Third step", "CookTime": "00:02:00", "Cookware": ["Spa<PERSON>la", "Bowl"], "Description": "With mixer on low speed, gradually add lightly beaten eggs, one at a time, stirring just until each egg is just incorporated.\n\nOnce all eggs have been added, use a spatula to scrape the sides and bottom of the bowl again and make sure all ingredients are well combined.", "Ingredients": ["Eggs"], "Number": 4, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "01:25:00", "Cookware": ["Oven", "<PERSON><PERSON>", "<PERSON><PERSON>", "Springform pan"], "Description": "Transfer to the center rack of your oven and bake on 325F (160C) for about 75 minutes.\n\nEdges will likely have slightly puffed and may have just begun to turn a light golden brown and the center should spring back to the touch but will still be Jello-jiggly.\n\nDon't over-bake or the texture will suffer, which means we all suffer.\n\nRemove from oven and allow to cool on top of the oven³ for 10 minutes.\n\nOnce 10 minutes has passed, use a knife to gently loosen the crust from the inside of the springform pan (this will help prevent cracks as your cheesecake cools and shrinks).", "Ingredients": ["Jello-jiggly", "Cream cheese"], "Number": 5, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/moms_cheesecake.png", "Serves": 1, "CookTime": "01:38:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/egg.png", "Name": "Eggs", "Quantity": "30 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/cheese.png", "Name": "Cream cheese", "Quantity": "100 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "50 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/jello.png", "Name": "Jello-jiggly", "Quantity": "50 g"}], "Calories": "700 kcal", "Reviews": [{"Id": "5dad6dce-339c-4af1-b99c-f530946a8a1e", "RecipeId": "f89a5305-4d77-4f66-a97c-65adba6b36ff", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Awesome as a mom gift", "Creator": {"Id": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "UrlProfileImage": "ms-appx:///Assets/Profiles/troyan_smith.png", "FullName": "<PERSON><PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 147, "Following": 259, "Recipes": 45}, "Category": {"Id": 1, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Breakfast", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": true}, {"Name": "<PERSON><PERSON><PERSON>", "UserId": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Id": "4761a59b-80bc-4910-b443-8ecc74565719", "Steps": [{"Name": "Getting started", "CookTime": "00:05:00", "Cookware": ["Bowl", "<PERSON>poon"], "Description": "Measure flour into a large mixing bowl.\n\nSlowly whisk in milk.\n\nWhisk in eggs, sugar, vanilla extract, cinnamon, and salt until smooth.", "Ingredients": ["Flour", "Milk", "Eggs", "Sugar", "Vanilla extract", "Cinnamon", "Salt"], "Number": 1, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:23:00", "Cookware": ["Bowl", "Oiled griddle", "Frying pan"], "Description": "Heat a lightly oiled griddle or frying pan over medium heat.\n\nSoak bread slices in milk mixture until saturated.", "Ingredients": ["Milk", "Eggs", "Vanilla"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:10:00", "Cookware": ["Batches", "Griddle", "<PERSON><PERSON><PERSON>"], "Description": "Working in batches, cook bread on the preheated griddle or pan until golden brown on each side.\n\nServe hot.", "Ingredients": ["Bread"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/fluffy_french_toast.png", "Serves": 1, "CookTime": "00:38:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/egg.png", "Name": "Eggs", "Quantity": "3"}, {"UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Bread", "Quantity": "50 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/milk.png", "Name": "Milk", "Quantity": "200 ml"}], "Calories": "400 kcal", "Reviews": [{"Id": "6b07573b-cf63-420a-937b-dd004682523b", "RecipeId": "4761a59b-80bc-4910-b443-8ecc74565719", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Good to have a pretty nice breakfast", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 1, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Breakfast", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": true}]}, {"Id": "a91c1918-f025-422a-9361-30f678b2bc3d", "Name": "Lunch", "UserId": "3c896419-e280-40e7-8552-240635566fed", "PinsNumber": 2, "Recipes": [{"Name": "Fresh Salad Thaid", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "0007e65f-3d5c-4ca3-842f-134b7f4a8b52", "Steps": [{"Name": "Getting started", "CookTime": "00:03:00", "Cookware": ["Knife", "Dish"], "Description": "Cut all your vegetables to size.\n\nYou can also use a bag of pre-shredded coleslaw.\n\nI diced the English cucumbers and cut the red bell peppers into thin strips.\n\nYou may also use julienne carrots, lettuce, etc.", "Ingredients": ["Carrot"], "Number": 1, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:06:00", "Cookware": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Knife", "Cutting Boards"], "Description": "Place the dressing ingredients in a jar and shake together until well combined.\n\nAdd the mix salad greens first, then strategically place the carrot, onion, red pepper and cucumber around the mixed greens.", "Ingredients": ["<PERSON><PERSON>", "Carrot", "Onion", "Red Pepper", "<PERSON><PERSON><PERSON>ber"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:01:00", "Cookware": ["Fork", "Knife", "Bowl"], "Description": "Finish up with the dressing.\n\nAdd the dressing just before you want to serve the salad otherwise it'll go soggy.", "Ingredients": ["Lime Juice", "Sesame Oil", "<PERSON>"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/fresh_salad_thaid.png", "Serves": 1, "CookTime": "00:10:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/lemon.png", "Name": "Lime Juice", "Quantity": "80 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/cucumber.png", "Name": "<PERSON><PERSON><PERSON>ber", "Quantity": "50 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/carrot.png", "Name": "Carrot", "Quantity": "50 g"}], "Calories": "350 kcal", "Reviews": [{"RecipeId": "0dc51562-67b7-4de8-91fa-a0a4a538d919", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}, {"RecipeId": "0dc51562-67b7-4de8-91fa-a0a4a538d919", "PublisherName": "<PERSON>", "UrlAuthorImage": "ms-appx:///Assets/Profiles/james_wolden.png", "CreatedBy": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Fresh, healthy and tasty", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 2, "UrlIcon": "ms-appx:///Assets/Icons/hamburger.png", "Name": "Lunch", "Color": "#507FF7"}, "Date": "2022-10-18T00:00:00Z", "Save": false}, {"Name": "Super Duper Oatmeal", "UserId": "3c896419-e280-40e7-8552-240635566fed", "Id": "63e275f8-9222-48b2-83cb-df6c0961a1eb", "Steps": [{"Name": "Getting started", "CookTime": "00:05:00", "Cookware": ["Bowl"], "Description": "Bring the milk and water to a boil in a bowl.\n\nCombine flours, oats, dried cranberries and apricots, baking powder, and salt in a large mixing bowl.", "Ingredients": ["Water", "Milk", "Oats", "Cranberries", "Apricots", "Salt"], "Number": 1, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:01:00", "Cookware": ["Pot", "<PERSON>poon"], "Description": "Mix in the oats, and reduce heat to medium.\n\nStir in blueberries, applesauce, wheat germ, cinnamon, and sugar.", "Ingredients": ["Oats", "Blueberries", "Applesauce", "Wheat germ", "Cinnamon", "Sugar"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:09:00", "Cookware": ["Pot"], "Description": "Cook 8 to 10 minutes, or until oats are tender.\n\nAs they finish, put them on a plate and hold in the oven while cooking remaining oats.", "Ingredients": ["Oats"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/super_duper_oatmeal.png", "Serves": 1, "CookTime": "00:15:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/blueberries.png", "Name": "Blueberries", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/green_apple.png", "Name": "Applesauce", "Quantity": "50 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/flour.png", "Name": "Oats", "Quantity": "50 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "300 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "400 gr"}], "Calories": "150 kcal", "Reviews": [{"Id": "b79797c0-5255-4937-ac02-4df5f231c8ce", "RecipeId": "63e275f8-9222-48b2-83cb-df6c0961a1eb", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Very special dessert to taste.", "Creator": {"Id": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "UrlProfileImage": "ms-appx:///Assets/Profiles/troyan_smith.png", "FullName": "<PERSON><PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 147, "Following": 259, "Recipes": 45}, "Category": {"Id": 1, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Breakfast", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}]}, {"Id": "f0d70ed4-a126-45a6-96a8-60b8b93dcd89", "Name": "Dinner", "UserId": "3c896419-e280-40e7-8552-240635566fed", "PinsNumber": 1, "Recipes": [{"Name": "Fresh Salad Pasta", "UserId": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Id": "b9e6502b-b373-4fcf-86b2-89379667aa5c", "Steps": [{"Name": "Getting started", "CookTime": "00:12:00", "Cookware": ["Pot"], "Description": "Bring a large pot of lightly salted water to a boil.\n\nCook pasta in the boiling water, stirring occasionally, until tender yet firm to the bite, about 10 to 12 minutes; rinse under cold water and drain.", "Ingredients": ["Pasta"], "Number": 1, "UrlVideo": ""}, {"Name": "Next step", "CookTime": "00:02:00", "Cookware": ["<PERSON><PERSON>"], "Description": "Whisk Italian dressing and salad spice mix together until smooth", "Ingredients": ["Spices"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:01:00", "Cookware": ["Salad bowl"], "Description": "Combine pasta, tomatoes, bell peppers, and olives in a salad bowl; pour dressing over salad and toss to coat.\n\nRefrigerate salad, 8 hours to overnight.", "Ingredients": ["Tomatoes", "Pasta", "Bell Peppers", "Olives"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/fresh_salad_pasta.png", "Serves": 1, "CookTime": "00:25:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt and pepper", "Quantity": "30 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/tomato.png", "Name": "Tomatoes", "Quantity": "100 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/olive.png", "Name": "Olives", "Quantity": "50 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/spaghetti.png", "Name": "Pasta", "Quantity": "50 g"}], "Calories": "350 kcal", "Reviews": [{"Id": "6a0fa592-1b1d-4855-ba83-7f6fd0120830", "RecipeId": "b9e6502b-b373-4fcf-86b2-89379667aa5c", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}, {"Id": "60d799c0-804e-4f48-9ad0-5a4def0bcbd2", "RecipeId": "b9e6502b-b373-4fcf-86b2-89379667aa5c", "PublisherName": "<PERSON>", "UrlAuthorImage": "ms-appx:///Assets/Profiles/james_wolden.png", "CreatedBy": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Healthy and tasty", "Creator": {"Id": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "UrlProfileImage": "ms-appx:///Assets/Profiles/troyan_smith.png", "FullName": "<PERSON><PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 147, "Following": 259, "Recipes": 45}, "Category": {"Id": 3, "UrlIcon": "ms-appx:///Assets/Icons/fork_and_knife_with_plate.png", "Name": "Dinner", "Color": "#F16583"}, "Date": "2022-10-15T00:00:00Z", "Save": false}]}, {"Id": "cd6f494f-3701-4b97-9199-41f5beacdc4d", "Name": "Love cookbook", "UserId": "3c896419-e280-40e7-8552-240635566fed", "PinsNumber": 4, "Recipes": [{"Name": "Circle Cake", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "1dc0e330-ab16-43cc-9b84-fdaf2c5e83cd", "Steps": [{"Name": "Getting started", "CookTime": "00:02:00", "Cookware": ["Bowl", "<PERSON>poon", "<PERSON><PERSON>", "Oven"], "Description": "Position a rack in the middle of the oven and preheat to 350 degrees.\n\nWhile the oven is heating, combine the flour, baking powder, and salt in a bowl, mixing well.", "Ingredients": ["Flour", "Baking powder", "Salt"], "Number": 1, "UrlVideo": ""}, {"Name": "First step", "CookTime": "00:05:00", "Cookware": ["Bowl", "Mixer"], "Description": "Place the butter and sugar in the bowl of a heavy-duty mixer fitted with the paddle attachment and beat on medium speed for about 5 minutes, or until very soft and light.\n\nBeat in the vanilla.", "Ingredients": ["Butter", "Sugar", "Vanilla"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:30:00", "Cookware": ["Toothpick", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "Description": "Bake the layers for about 30 to 35 minutes, until they are well risen and firm and a toothpick inserted in the center emerges clean.\n\nCool the layers in the pans on racks for 5 minutes, then unmold onto racks to finish cooling right side up.", "Ingredients": ["Butter"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/circle_cake.png", "Serves": 1, "CookTime": "00:37:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/butter.png", "Name": "Butter", "Quantity": "500 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "50 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/flour.png", "Name": "Flour", "Quantity": "1000 gr"}], "Calories": "550 kcal", "Reviews": [{"Id": "9de1691e-da43-4788-8a98-0e5b2f2a2a6c", "RecipeId": "1dc0e330-ab16-43cc-9b84-fdaf2c5e83cd", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}, {"Id": "8a8e8398-049f-466c-8f7e-634c30815cd8", "RecipeId": "1dc0e330-ab16-43cc-9b84-fdaf2c5e83cd", "PublisherName": "<PERSON>", "UrlAuthorImage": "ms-appx:///Assets/Profiles/james_wolden.png", "CreatedBy": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Impressive with a very fancy form", "Creator": {"Id": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "UrlProfileImage": "ms-appx:///Assets/Profiles/troyan_smith.png", "FullName": "<PERSON><PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 147, "Following": 259, "Recipes": 45}, "Category": {"Id": 4, "UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Snack", "Color": "#CAC2FC"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Mom's Cheesecake", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "f89a5305-4d77-4f66-a97c-65adba6b36ff", "Steps": [{"Name": "Getting started", "CookTime": "00:11:00", "Cookware": ["<PERSON><PERSON>", "Bowl"], "Description": "In the bowl of a stand mixer or in a large bowl (using a hand mixer) add cream cheese and stir until smooth and creamy (don’t over-beat or you’ll incorporate too much air).\n\nAdd sugar and stir again until creamy.", "Ingredients": ["Sugar", "Cream Cheese"], "Number": 1, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:01:00", "Cookware": ["<PERSON><PERSON>", "Spa<PERSON>la"], "Description": "Add sour cream, vanilla extract, and salt, and stir until well-combined.\n\nIf using a stand mixer, make sure you pause periodically to scrape the sides and bottom of the bowl with a spatula so that all ingredients are evenly incorporated", "Ingredients": ["Sour cream, Vanilla extract"], "Number": 3, "UrlVideo": ""}, {"Name": "Third step", "CookTime": "00:02:00", "Cookware": ["Spa<PERSON>la", "Bowl"], "Description": "With mixer on low speed, gradually add lightly beaten eggs, one at a time, stirring just until each egg is just incorporated.\n\nOnce all eggs have been added, use a spatula to scrape the sides and bottom of the bowl again and make sure all ingredients are well combined.", "Ingredients": ["Eggs"], "Number": 4, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "01:25:00", "Cookware": ["Oven", "<PERSON><PERSON>", "<PERSON><PERSON>", "Springform pan"], "Description": "Transfer to the center rack of your oven and bake on 325F (160C) for about 75 minutes.\n\nEdges will likely have slightly puffed and may have just begun to turn a light golden brown and the center should spring back to the touch but will still be Jello-jiggly.\n\nDon't over-bake or the texture will suffer, which means we all suffer.\n\nRemove from oven and allow to cool on top of the oven³ for 10 minutes.\n\nOnce 10 minutes has passed, use a knife to gently loosen the crust from the inside of the springform pan (this will help prevent cracks as your cheesecake cools and shrinks).", "Ingredients": ["Jello-jiggly", "Cream cheese"], "Number": 5, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/moms_cheesecake.png", "Serves": 1, "CookTime": "01:38:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/egg.png", "Name": "Eggs", "Quantity": "30 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/cheese.png", "Name": "Cream cheese", "Quantity": "100 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "50 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/jello.png", "Name": "Jello-jiggly", "Quantity": "50 g"}], "Calories": "700 kcal", "Reviews": [{"Id": "5dad6dce-339c-4af1-b99c-f530946a8a1e", "RecipeId": "f89a5305-4d77-4f66-a97c-65adba6b36ff", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Awesome as a mom gift", "Creator": {"Id": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "UrlProfileImage": "ms-appx:///Assets/Profiles/troyan_smith.png", "FullName": "<PERSON><PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 147, "Following": 259, "Recipes": 45}, "Category": {"Id": 1, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Breakfast", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": true}, {"Name": "<PERSON><PERSON><PERSON>", "UserId": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Id": "4761a59b-80bc-4910-b443-8ecc74565719", "Steps": [{"Name": "Getting started", "CookTime": "00:05:00", "Cookware": ["Bowl", "<PERSON>poon"], "Description": "Measure flour into a large mixing bowl.\n\nSlowly whisk in milk.\n\nWhisk in eggs, sugar, vanilla extract, cinnamon, and salt until smooth.", "Ingredients": ["Flour", "Milk", "Eggs", "Sugar", "Vanilla extract", "Cinnamon", "Salt"], "Number": 1, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:23:00", "Cookware": ["Bowl", "Oiled griddle", "Frying pan"], "Description": "Heat a lightly oiled griddle or frying pan over medium heat.\n\nSoak bread slices in milk mixture until saturated.", "Ingredients": ["Milk", "Eggs", "Vanilla"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:10:00", "Cookware": ["Batches", "Griddle", "<PERSON><PERSON><PERSON>"], "Description": "Working in batches, cook bread on the preheated griddle or pan until golden brown on each side.\n\nServe hot.", "Ingredients": ["Bread"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/fluffy_french_toast.png", "Serves": 1, "CookTime": "00:38:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/egg.png", "Name": "Eggs", "Quantity": "3"}, {"UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Bread", "Quantity": "50 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/milk.png", "Name": "Milk", "Quantity": "200 ml"}], "Calories": "400 kcal", "Reviews": [{"Id": "6b07573b-cf63-420a-937b-dd004682523b", "RecipeId": "4761a59b-80bc-4910-b443-8ecc74565719", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Good to have a pretty nice breakfast", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 1, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Breakfast", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": true}, {"Name": "Super Duper Oatmeal", "UserId": "3c896419-e280-40e7-8552-240635566fed", "Id": "63e275f8-9222-48b2-83cb-df6c0961a1eb", "Steps": [{"Name": "Getting started", "CookTime": "00:05:00", "Cookware": ["Bowl"], "Description": "Bring the milk and water to a boil in a bowl.\n\nCombine flours, oats, dried cranberries and apricots, baking powder, and salt in a large mixing bowl.", "Ingredients": ["Water", "Milk", "Oats", "Cranberries", "Apricots", "Salt"], "Number": 1, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:01:00", "Cookware": ["Pot", "<PERSON>poon"], "Description": "Mix in the oats, and reduce heat to medium.\n\nStir in blueberries, applesauce, wheat germ, cinnamon, and sugar.", "Ingredients": ["Oats", "Blueberries", "Applesauce", "Wheat germ", "Cinnamon", "Sugar"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:09:00", "Cookware": ["Pot"], "Description": "Cook 8 to 10 minutes, or until oats are tender.\n\nAs they finish, put them on a plate and hold in the oven while cooking remaining oats.", "Ingredients": ["Oats"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/super_duper_oatmeal.png", "Serves": 1, "CookTime": "00:15:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/blueberries.png", "Name": "Blueberries", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/green_apple.png", "Name": "Applesauce", "Quantity": "50 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/flour.png", "Name": "Oats", "Quantity": "50 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "300 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "400 gr"}], "Calories": "150 kcal", "Reviews": [{"Id": "b79797c0-5255-4937-ac02-4df5f231c8ce", "RecipeId": "63e275f8-9222-48b2-83cb-df6c0961a1eb", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Very special dessert to taste.", "Creator": {"Id": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "UrlProfileImage": "ms-appx:///Assets/Profiles/troyan_smith.png", "FullName": "<PERSON><PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 147, "Following": 259, "Recipes": 45}, "Category": {"Id": 1, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Breakfast", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}]}, {"Id": "b5f06c19-fe1b-4f9e-8602-27ca804dae27", "Name": "Awesome", "UserId": "3c896419-e280-40e7-8552-240635566fed", "PinsNumber": 3, "Recipes": [{"Name": "Walnut and nuts", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "f649eefe-93cb-4bca-93a2-af707966d217", "Steps": [{"Name": "Getting started", "CookTime": "00:02:00", "Cookware": ["Bowl"], "Description": "In a bowl, combine flour and sugar.\n\nCut in butter until mixture resembles coarse crumbs.\n\nCombine egg yolks and milk; stir into flour mixture until blended.\n\nWith lightly floured hands, press dough onto the bottom and 1 in.", "Ingredients": ["Flour", "Sugar", "Eggs", "Milk"], "Number": 1, "UrlVideo": ""}, {"Name": "Second Step", "CookTime": "00:10:00", "Cookware": ["Knife", "Baking Sheet", "Pan"], "Description": "Up the sides of a 12-in.\n\nTart pan with removable bottom.\n\nLine unpricked crust with a double thickness of heavy-duty foil.\n\nFill with pie weights.\n\nPlace pan on a baking sheet.\n\nBake at 375° until edges are lightly browned, 12-15 minutes.", "Ingredients": ["Eggs", "Milk"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:20:00", "Cookware": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>poon", "Wire rack"], "Description": "Meanwhile, in a saucepan, combine the sugar, cream, cinnamon and salt.\n\nBring to a boil over medium heat, stirring constantly.\n\nRemove from the heat; stir in walnuts.\n\nRemove foil from crust; pour filling into crust.\n\nBake until golden brown, 20-25 minutes.\n\nCool on a wire rack.\n\nStore in the refrigerator.", "Ingredients": ["Sugar", "Cream", "Cinnamon", "Salt", "Peanuts"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/walnut_and_nuts.png", "Serves": 1, "CookTime": "00:32:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/peanuts.png", "Name": "Peanuts", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/egg.png", "Name": "Eggs", "Quantity": "5"}, {"UrlIcon": "ms-appx:///Assets/Icons/green_apple.png", "Name": "Applesauce", "Quantity": "300 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt", "Quantity": "30 gr"}], "Calories": "150 kcal", "Reviews": [{"Id": "92ebde78-0049-4e8c-af6f-4e7dc6d6c0b2", "RecipeId": "f649eefe-93cb-4bca-93a2-af707966d217", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Walnuts recipe to get very taste dessert", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 1, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Breakfast", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Avocado Salad", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "f649eefe-93cb-4bca-93a2-af806966d217", "Steps": [{"Name": "Getting started", "CookTime": "00:05:00", "Cookware": ["Mixing bowl"], "Description": "In a small mixing bowl whisk together lemon juice, red wine vinegar, extra virgin oil oil, honey, garlic.", "Ingredients": ["Lemon juic", "Red wine vinegar", "Extra virgin oil oil", "Honey", "<PERSON><PERSON><PERSON>"], "Number": 1, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:10:00", "Cookware": ["Mixing bowl"], "Description": "After whisk, to get better taste pour cilantro, parsley, oregano, and season with salt and pepper.", "Ingredients": ["Cilantro", "<PERSON><PERSON><PERSON>", "Oregano", "Salt", "Pepper"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:10:00", "Cookware": ["Bowl"], "Description": "In a large bowl gently toss together cucumbers, tomatoes, red onion, avocado with dressing.", "Ingredients": ["Cucumbers", "Tomatoes", "Red onion", "Avocado"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/avocado_salad.png", "Serves": 1, "CookTime": "00:25:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/lemon.png", "Name": "Lemon juice", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/olive.png", "Name": "Red wine vinegar", "Quantity": "10 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/honey.png", "Name": "Honey", "Quantity": "7 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/garlic.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "1"}, {"UrlIcon": "ms-appx:///Assets/Icons/parsley.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "3 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt and pepper", "Quantity": "3 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/cucumber.png", "Name": "Cucumbers", "Quantity": "5 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/tomato.png", "Name": "Tomatoes", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/onion.png", "Name": "Red onion", "Quantity": "4 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/avocado.png", "Name": "Avocado", "Quantity": "10 gr"}], "Calories": "200 kcal", "Reviews": [{"Id": "0d191b8a-993c-47b0-908c-30c5bc57980e", "RecipeId": "f649eefe-93cb-4bca-93a2-af806966d217", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Avocado salad recipe is a very healthy meal to loss weight", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 2, "UrlIcon": "ms-appx:///Assets/Icons/hamburger.png", "Name": "Lunch", "Color": "#507FF7"}, "Date": "2022-10-18T00:00:00Z", "Save": false}, {"Name": "Croissants", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "f649eefe-57cb-4bca-93a2-af707966d217", "Steps": [{"Name": "Getting started", "CookTime": "00:20:00", "Cookware": ["Bowl", "Mixer", "<PERSON>h hook"], "Description": "Twenty-four hours before serving, start the détrempe: In the bowl of a stand mixer fitted with the dough hook, combine the flour, sugar, salt and yeast, and stir to combine.", "Ingredients": ["Flour", "Sugar", "Salt", "Honey", "<PERSON><PERSON><PERSON>", "Yeast"], "Number": 1, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:15:00", "Cookware": ["Mixer", "<PERSON>h hook", "<PERSON><PERSON><PERSON>"], "Description": "Create a well in the center, and pour in the water and milk.\n\nMix on low speed until a tight, smooth dough comes together around the hook, about 5 minutes.\n\nRemove the hook and cover the bowl with a damp towel.\n\nSet aside for 10 minutes.", "Ingredients": ["Water", "Milk"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:10:00", "Cookware": ["<PERSON>h hook", "Mixer", "Bowl"], "Description": "Reattach the dough hook and turn the mixer on medium-low speed.\n\nAdd the butter pieces all at once and continue to mix, scraping down the bowl and hook once or twice, until the dough has formed a very smooth, stretchy ball that is not the least bit sticky, 8 to 10 minutes.", "Ingredients": ["Butter"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/croissants.png", "Serves": 1, "CookTime": "00:45:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/butter.png", "Name": "Butter", "Quantity": "20 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/flour.png", "Name": "Flour", "Quantity": "50 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "5 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt", "Quantity": "6 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/honey.png", "Name": "Honey", "Quantity": "7 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/garlic.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "1"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "200 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/milk.png", "Name": "Milk", "Quantity": "150 ml"}], "Calories": "800 kcal", "Reviews": [{"Id": "e902d8b8-3167-44d2-859b-51ec28ab772d", "RecipeId": "f649eefe-57cb-4bca-93a2-af707966d217", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Crossiant recipe to get very taste after lunch", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 1, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Breakfast", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}]}, {"Id": "0e0f1a6c-af81-4893-8c51-1ed7b6d3800f", "Name": "Interesting", "UserId": "3c896419-e280-40e7-8552-240635566fed", "PinsNumber": 5, "Recipes": [{"Name": "Strawberry Cream Pie", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "f649eefe-26cb-4bca-93a2-af707012d237", "Steps": [{"Name": "Getting start", "CookTime": "00:10:00", "Cookware": ["Bowl", "Cups"], "Description": "In a medium bowl, mix together 1 1/2 cups of all-purpose flour and 1/2 cup of cold butter.", "Ingredients": ["Flour", "Butter"], "Number": 1, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:20:00", "Cookware": ["Pie dish", "Rolling pin"], "Description": "Use a fork or pastry cutter to blend the butter into the flour until it resembles coarse crumbs.\n\nSlowly add in 1/4 cup of ice water, 1 tablespoon at a time, and mix until the dough comes together.\n\nRoll out the dough on a floured surface to fit your pie dish and press it into the bottom and up the sides.\n\nPrick the crust all over with a fork and preheat the oven to 375F.\n\nBake the crust for 15-20 minutes, or until lightly golden brown.", "Ingredients": ["Flour", "Butter", "Water"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:25:00", "Cookware": ["Medium saucepan", "Whisk"], "Description": "In a medium saucepan, mix together 1 cup of heavy cream, 1/2 cup of granulated sugar, and 1 teaspoon of vanilla extract.\n\nHeat the mixture over medium heat, whisking constantly, until it comes to a simmer.\n\nIn a small bowl, mix together 2 tablespoons of cornstarch and 2 tablespoons of cold water to make a slurry.\n\nSlowly pour the slurry into the saucepan and continue to whisk until the mixture thickens.\n\nRemove from heat and stir in 2 cups of sliced strawberries.\n\nPour the filling into the cooled crust and refrigerate for at least 2 hours before serving.", "Ingredients": ["Cream", "Sugar", "Vanilla extract", "Cornstarch", "Water", "Strawberries"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/strawberry_cream_pie.png", "Serves": 1, "CookTime": "00:55:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/flour.png", "Name": "Flour", "Quantity": "50 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/butter.png", "Name": "Butter", "Quantity": "150 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "200 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "150 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Strawberries", "Quantity": "30 gr"}], "Calories": "300 kcal", "Reviews": [{"Id": "8ca65778-87b7-4417-94c3-8df09ac14dd8", "RecipeId": "f649eefe-26cb-4bca-93a2-af707012d237", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Strawberry Cream Pie recipe is an awusome dessert to eat in any moment", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 1, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Breakfast", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "<PERSON><PERSON><PERSON>", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "f167eefe-78cb-4bca-93a2-af707012d237", "Steps": [{"Name": "Getting start", "CookTime": "00:05:00", "Cookware": ["Bowl"], "Description": "Combine sauce ingredients and stir until brown sugar is dissolved.", "Ingredients": ["Sauce", "Sugar"], "Number": 1, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:20:00", "Cookware": ["Bowl", "Plastic wrap"], "Description": "Place individual salmon slices in a mixing bowl.\n\nPour the sauce over the salmon, cover with plastic wrap and let marinate 20 minutes (at room temp or refrigerated).", "Ingredients": ["Salmon", "Sauce"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:25:00", "Cookware": ["Baking sheet", "Knife"], "Description": "Transfer salmon to prepared baking sheet (keep the marinade).\n\nBake at 400 for 12-16 minutes or until salmon is flaky and cooked through, bake times may vary by thickness and cut of salmon (see notes on how long to bake salmon below).", "Ingredients": ["Salmon"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/teriyaki_salmon.png", "Serves": 1, "CookTime": "00:50:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/salmon.png", "Name": "Salmon", "Quantity": "300 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sauce.png", "Name": "Sauce", "Quantity": "100 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "50 mgr"}], "Calories": "350 kcal", "Reviews": [{"Id": "e1674b3f-4833-423f-a90b-472867f22e58", "RecipeId": "f167eefe-78cb-4bca-93a2-af707012d237", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Teriyaki Salmon is a high protein food with lots of nutrients", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 3, "UrlIcon": "ms-appx:///Assets/Icons/fork_and_knife_with_plate.png", "Name": "Dinner", "Color": "#F16583"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Air Fryer French Fries", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "f467eefe-67cb-6bca-08a2-af707012d237", "Steps": [{"Name": "Getting start", "CookTime": "00:05:00", "Cookware": ["Potato peeler"], "Description": "We just wash the potatoes and keep the skins on, but you can peel if you prefer.", "Ingredients": ["Potatoes"], "Number": 1, "UrlVideo": ""}, {"Name": "First step", "CookTime": "00:05:00", "Cookware": ["Knife"], "Description": "Clean the potatoes and cut into about 1/4 inch strands, making sure all the strands are the same size for even cooking.\n\nRinse the fries under cold water and pat dry.", "Ingredients": ["Potatoes", "Water"], "Number": 2, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:10:00", "Cookware": ["Bowl", "Air fryer"], "Description": "Add the fries into a bowl and drizzle with oil and toss, season and toss again.\n\nEvenly spread the fries into the air fryer basket.", "Ingredients": ["Potatoes", "Oil"], "Number": 3, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:05:00", "Cookware": ["Bowl", "Air fryer"], "Description": "Cook the fries until crispy and golden, tossing half way.", "Ingredients": ["Potatoes"], "Number": 4, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/air_fryer_french_fries.png", "Serves": 1, "CookTime": "00:25:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/potato.png", "Name": "Potatoes", "Quantity": "500 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "400 ml"}], "Calories": "425 kcal", "Reviews": [{"Id": "25370a3f-7bca-4475-9575-3a7b54752e76", "RecipeId": "f467eefe-67cb-6bca-08a2-af707012d237", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Air Fryer French Fries is a delicious plate and more healthy that normal fries", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 4, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Snack", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "<PERSON><PERSON><PERSON> Potatoes", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "f907eefe-67cb-6bca-08a2-af356012d237", "Steps": [{"Name": "Getting start", "CookTime": "00:15:00", "Cookware": ["Oven"], "Description": "Preheat the oven to 425ºF", "Ingredients": ["Potatoes"], "Number": 1, "UrlVideo": ""}, {"Name": "First step", "CookTime": "00:10:00", "Cookware": ["Chopstick", "Cutting board", "Knife"], "Description": "Set a potato on a cutting board and place a chopstick on either side of the potato.\n\nWith a sharp, thin knife, make deep vertical cuts 1/8-inch apart, but without cutting all the way through the potato.\n\nThe chopsticks should keep you from accidentally cutting too deeply or going all the way through.", "Ingredients": ["Potatoes"], "Number": 2, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:05:00", "Cookware": ["Baking dish"], "Description": "Place the potatoes with the cut side up in the baking dish, spaced a little apart so each one has some room.\n\nFan the potatoes open slightly.", "Ingredients": ["Potatoes"], "Number": 3, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:30:00", "Cookware": ["Oven"], "Description": "Or until golden and crispy.\n\nThe potatoes will fan out more during cooking and take on their accordion-like appearance.\n\nServe hot.", "Ingredients": ["Potatoes"], "Number": 4, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/hasselback_potatoes.png", "Serves": 1, "CookTime": "00:55:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/potato.png", "Name": "Potatoes", "Quantity": "500 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "400 ml"}], "Calories": "375 kcal", "Reviews": [{"Id": "40c710b9-e8bb-40f6-9115-03382aa138fa", "RecipeId": "f907eefe-67cb-6bca-08a2-af356012d237", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Hasselback Potatoes is a delicious plate", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 2, "UrlIcon": "ms-appx:///Assets/Icons/hamburger.png", "Name": "Lunch", "Color": "#507FF7"}, "Date": "2022-10-18T00:00:00Z", "Save": false}, {"Name": "Stir-Fry Chicken and Vegetables", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "f907eefe-67cb-6bca-08a2-af716322d237", "Steps": [{"Name": "Getting start", "CookTime": "00:10:00", "Cookware": ["Pan", "Tablespoon"], "Description": "In a large pan on medium-high heat, add 1 tablespoon of oil.\n\nOnce the oil is hot, add chicken, season with salt and pepper, and sauté until cooked through and browned.\n\nRemove cooked chicken from pan and set aside.", "Ingredients": ["Oil", "Chicken", "Salt", "Pepper"], "Number": 1, "UrlVideo": ""}, {"Name": "First step", "CookTime": "00:05:00", "Cookware": ["Pan", "Tablespoon"], "Description": "In the same pan, heat 1 tablespoon of oil and add mushrooms.\n\nWhen the mushrooms start to soften, add broccoli florets and stir-fry until the broccoli is tender.\n\nRemove cooked mushrooms and broccoli from the pan and set aside", "Ingredients": ["<PERSON><PERSON><PERSON><PERSON>"], "Number": 2, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:10:00", "Cookware": ["Pan", "Tablespoon"], "Description": "Add 1 tablespoon of oil to the pan and sauté garlic and ginger until fragrant.\n\nAdd the remaining sauce ingredients and stir until smooth.", "Ingredients": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Sauce"], "Number": 3, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:07:00", "Cookware": ["Pan"], "Description": "Return the chicken and vegetables to the saucy pan, stir until heated through.", "Ingredients": ["Chicken"], "Number": 4, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/stir_fry_chicken_salad.png", "Serves": 1, "CookTime": "00:32:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/chicken.png", "Name": "Chicken", "Quantity": "455 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt and pepper", "Quantity": "1 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/broccoli.png", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Quantity": "455 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/garlic.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "3"}, {"UrlIcon": "ms-appx:///Assets/Icons/sauce.png", "Name": "Sauce", "Quantity": "80 ml"}], "Calories": "170 kcal", "Reviews": [{"Id": "d38d9ce4-505b-406a-9f2a-637035f62a95", "RecipeId": "f907eefe-67cb-6bca-08a2-af716322d237", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Stir-Fry Chicken and Vegetables low calories meal", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 2, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Lunch", "Color": "#507FF7"}, "Date": "2022-10-15T00:00:00Z", "Save": false}]}, {"Id": "9b019326-f81a-4ecb-bca3-a6ecf8d2357b", "Name": "Fast", "UserId": "3c896419-e280-40e7-8552-240635566fed", "PinsNumber": 1, "Recipes": [{"Name": "<PERSON><PERSON><PERSON>", "UserId": "3c896419-e280-40e7-8552-240635566fed", "Id": "052e70cb-f800-46aa-bc10-f21d10dadb14", "Steps": [{"Name": "Getting start", "CookTime": "00:15:00", "Cookware": ["Plate"], "Description": "Get bread, hummus and avocado to get ready the ingredients. Then start to spread one slice of bread with hummus and the other with avocado.", "Ingredients": ["Avocado", "Bread", "<PERSON>mus"], "Number": 1, "UrlVideo": ""}, {"Name": "First step", "CookTime": "00:10:00", "Cookware": ["Plate"], "Description": "For this step, it's important to get several vegetables that provide the recipe nutrients.\n\nFill the sandwich with greens, bell pepper, cucumber and carrot.", "Ingredients": ["Greens", "Pepper", "<PERSON><PERSON><PERSON>ber", "Carrot"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:05:00", "Cookware": ["Plate"], "Description": "For getting the nutrients, it's very important to follow the last two steps very strict.\n\n Slice in half and serve.", "Ingredients": ["Bread"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/veggie_sandwich.png", "Serves": 1, "CookTime": "00:10:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/avocado.png", "Name": "Avocado", "Quantity": "25 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Bread", "Quantity": "30 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/leafy_green.png", "Name": "Greens", "Quantity": "32.5 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Pepper", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/cucumber.png", "Name": "<PERSON><PERSON><PERSON>ber", "Quantity": "75 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/carrot.png", "Name": "Carrot", "Quantity": "64 gr"}], "Calories": "265 kcal", "Reviews": [{"Id": "0b6004c6-ea94-4716-b9c9-889c3570ed84", "RecipeId": "052e70cb-f800-46aa-bc10-f21d10dadb14", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "This is a traditional meal in italia", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 4, "UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Snack", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}]}, {"Id": "25dfce85-b772-49d4-8d46-b3b2d3a2c0a2", "Name": "Restaurant food", "UserId": "3c896419-e280-40e7-8552-240635566fed", "PinsNumber": 2, "Recipes": [{"Name": "Prawns Provencale", "UserId": "3c896419-e280-40e7-8552-240635566fed", "Id": "6e3e90fb-f2fe-4a98-81ff-85033a8a4010", "Steps": [{"Name": "Getting start", "CookTime": "00:12:00", "Cookware": ["Sheep pan", "Aluminum foil", "Tablespoon", "<PERSON>mp"], "Description": "Preheat oven to 475 degrees F (245 degrees C). Line a sheet pan with aluminum foil. Brush with 1 tablespoon olive oil.\n\nCarefully remove shells and legs of prawns; leave the tail attached. To butterfly the prawns, cut a slit with a small sharp knife lengthwise down the belly side of shrimp, almost to the skin on the back. Open out like a book.", "Ingredients": ["<PERSON>mp", "Prawns", "Olive oil"], "Number": 1, "UrlVideo": ""}, {"Name": "First step", "CookTime": "00:03:00", "Cookware": ["Mortar", "Tablespoon", "Cup"], "Description": "Place chopped garlic, large pinch kosher salt, oregano, and thyme in a mortar; pound and stir with a pestle a few seconds. Add 1 tablespoon fresh parsley.\n\nPound and stir mixture until it turns into a paste, 1 or 2 minutes. Add 1/3 cup olive oil; mix about 1 minute to infuse olive oil with herbs and garlic.", "Ingredients": ["<PERSON><PERSON><PERSON>", "Oregano", "Thyme", "<PERSON><PERSON><PERSON>", "Olive oil"], "Number": 2, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:03:00", "Cookware": ["Bowl", "Fork"], "Description": "Place bread crumbs in a mixing bowl. Transfer herb-garlic mixture to breadcrumbs. Add a pinch of salt, black pepper, pinch cayenne, remaining chopped parsley, and grated cheese. Mix with a fork to distribute ingredients evenly. Pinch a bit of the mixture; if it feels a bit dry and doesn't stick to your finger, drizzle in a bit more olive oil. Stir until mixture reaches desired consistency. 2 to 3 minutes.", "Ingredients": ["Bread crumbs", "<PERSON><PERSON><PERSON>", "Grated cheese", "<PERSON><PERSON><PERSON>", "Cayenne"], "Number": 3, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:10:00", "Cookware": ["Baking sheet"], "Description": "Lightly but thoroughly coat cut side of prawns with crumb mixture; place on prepared baking sheet.\n\nBake in preheated oven until cooked through and tails curl up, 8 to 10 minutes.", "Ingredients": ["Prawns", "Bread crumb"], "Number": 4, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/prawns_provencale.png", "Serves": 1, "CookTime": "00:28:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/olive.png", "Name": "Olive oil", "Quantity": "20 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Bread crumbs", "Quantity": "150 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/prawns.png", "Name": "Prawns", "Quantity": "30 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt and pepper", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/parsley.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "8.5 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/cheese.png", "Name": "Grated cheese", "Quantity": "42.5 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/hot_pepper.png", "Name": "Cayenne", "Quantity": "5.3 gr"}], "Calories": "385 kcal", "Reviews": [{"Id": "f84b52c1-3536-41ca-a077-3d423de32ff8", "RecipeId": "6e3e90fb-f2fe-4a98-81ff-85033a8a4010", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Provide seafood benefits and calories", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 4, "UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Snack", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Salmon Croquettes", "UserId": "3c896419-e280-40e7-8552-240635566fed", "Id": "256cdf86-04b3-444f-b496-a364d280d66f", "Steps": [{"Name": "Getting start", "CookTime": "00:05:00", "Cookware": ["Bowl"], "Description": "Add panko and flour to a bowl and mix.\nAdd bell peppers, canned salmon, garlic, salt, pepper, egg, mayonnaise, Worcestershire sauce and cilantro. Mix until incorporated.", "Ingredients": ["<PERSON><PERSON>", "Flour", "Pepper", "Canned salmon", "<PERSON><PERSON><PERSON>", "Salt"], "Number": 1, "UrlVideo": ""}, {"Name": "First step", "CookTime": "00:07:00", "Cookware": ["<PERSON><PERSON><PERSON>"], "Description": "Shape into 6-8 patties and heat oil in large skillet over medium high heat.", "Ingredients": ["Oil"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:03:00", "Cookware": ["<PERSON><PERSON><PERSON>"], "Description": "Add patties to the skillet and cook for 2-3 minutes on each side or until golden brown.", "Ingredients": ["Pepper", "Egg", "Mayonnaise", "Worcestershire sauce", "Cilantro"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/salmon_croquettes.png", "Serves": 1, "CookTime": "00:15:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/panko.png", "Name": "<PERSON><PERSON>", "Quantity": "50 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/flour.png", "Name": "Flour", "Quantity": "34 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt and pepper", "Quantity": "2.5 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salmon.png", "Name": "Canned salmon", "Quantity": "141 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/garlic.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "5 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/egg.png", "Name": "Egg", "Quantity": "1"}, {"UrlIcon": "ms-appx:///Assets/Icons/mayonnaise.png", "Name": "Mayonnaise", "Quantity": "2.5 gr"}], "Calories": "150 kcal", "Reviews": [{"Id": "52728a86-a404-42be-a664-4c2e02aa250b", "RecipeId": "256cdf86-04b3-444f-b496-a364d280d66f", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Provide seafood benefits and calories", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 4, "UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Snack", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}]}, {"Id": "f438a1ab-6307-4d54-9cf0-1418a227fb42", "Name": "Happy", "UserId": "3c896419-e280-40e7-8552-240635566fed", "PinsNumber": 3, "Recipes": [{"Name": "Homemade lasagna", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "be9c0be1-3980-4331-b6df-0975de70251a", "Steps": [{"Name": "Getting start", "CookTime": "00:30:00", "Cookware": ["Pot", "Tablespoon"], "Description": "Put a large pot of salted water (1 tablespoon of salt for every 2 quarts of water) on the stovetop on high heat.\n\nIt can take a while for a large pot of water to come to a boil (this will be your pasta water), so prepare the sauce in the next steps while the water is heating.", "Ingredients": ["Water", "Salt", "Sauce"], "Number": 1, "UrlVideo": ""}, {"Name": "First step", "CookTime": "00:20:00", "Cookware": ["<PERSON><PERSON><PERSON>", "Tablespoon", "<PERSON>poon", "Bowl"], "Description": "In a large skillet heat 2 teaspoons of olive oil on medium-high heat.\n\nAdd the ground beef and cook until it is lightly browned on all sides.\n\nRemove the beef with a slotted spoon to a bowl.\n\nDrain off all but a tablespoon of fat.", "Ingredients": ["Olive oil", "Ground beef"], "Number": 2, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:10:00", "Cookware": ["Pan", "Tablespoon"], "Description": "Add the diced bell pepper and onions to the skillet (in the photo we are using yellow bell pepper and red onions).\n\nCook for 4 to 5 minutes, until the onions are translucent and the peppers softened.\n\nAdd the minced garlic and cook half a minute more.\n\nReturn the browned ground beef to the pan.\n\nStir to combine, reduce the heat to low, and cook for another 5 minutes.", "Ingredients": ["Pepper", "Onions", "<PERSON><PERSON><PERSON>", "Ground beef"], "Number": 3, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:45:00", "Cookware": ["Pan", "Aluminum foil"], "Description": "Cover the lasagna pan with aluminum foil, tented slightly so it doesn't touch the noodles or sauce.\n\nBake at 375°F for 45 minutes. Uncover in the last 10 minutes if you'd like more of a crusty top or edges.", "Ingredients": ["Sauce"], "Number": 4, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/homemade_lasagna.png", "Serves": 1, "CookTime": "01:45:00", "Difficulty": 1, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/olive.png", "Name": "Olive oil", "Quantity": "20 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "100 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt and pepper", "Quantity": "2 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sauce.png", "Name": "Sauce", "Quantity": "828 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/meat.png", "Name": "Ground beef", "Quantity": "454 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/onion.png", "Name": "Onions", "Quantity": "26 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/garlic.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "2"}], "Calories": "425 kcal", "Reviews": [{"Id": "537802b0-aa01-4e1d-8dd6-c6c60c8614e6", "RecipeId": "be9c0be1-3980-4331-b6df-0975de70251a", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Homemade lasagna very taste to eat from home", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 2, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Lunch", "Color": "#507FF7"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Pork Stew", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "a0e8131c-4853-4d3f-a2d7-b6f54dc38a41", "Steps": [{"Name": "Getting start", "CookTime": "00:20:00", "Cookware": ["Pot", "Tablespoon"], "Description": "In a large pot or dutch oven, brown pork with onions in 1 tablespoon olive oil (it doesn't have to be cooked through).\n\nRemove from pot and set aside.", "Ingredients": ["Pork", "Onion", "Olive oil"], "Number": 1, "UrlVideo": ""}, {"Name": "First step", "CookTime": "01:00:00", "Cookware": ["Pot"], "Description": "Add broth, tomatoes, and spices making sure to scrape up any brown bits from the bottom of the pot.\n\nAdd the pork back into the pot and bring to a boil, reduce heat and simmer covered for 1 hour (or until pork is fairly tender).", "Ingredients": ["Tomato", "Spice", "Pork"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:40:00", "Cookware": ["Pot"], "Description": "Add potatoes, sweet potatoes, carrots, and mushrooms.\n\nBring to a boil, reduce heat and simmer covered for 30 minutes or until vegetables are tender.\n\nRemove lid and stir in green beans.\n\nThicken if desired (below) and simmer an additional 10 minutes uncovered.", "Ingredients": ["Potato", "Carrot", "Mushroom", "Green beans"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/pork_stew.png", "Serves": 1, "CookTime": "02:00:00", "Difficulty": 1, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/olive.png", "Name": "Olive oil", "Quantity": "20 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/pork.png", "Name": "Pork", "Quantity": "680 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/tomato.png", "Name": "Tomato", "Quantity": "425 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/potatoe.png", "Name": "Potato", "Quantity": "156 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/carrot.png", "Name": "Carrot", "Quantity": "2"}, {"UrlIcon": "ms-appx:///Assets/Icons/onion.png", "Name": "Onions", "Quantity": "26 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/mushroom.png", "Name": "Mushrooms", "Quantity": "96 gr"}], "Calories": "550 kcal", "Reviews": [{"Id": "5d5f45db-dd02-449c-acd9-10b3dc51e011", "RecipeId": "a0e8131c-4853-4d3f-a2d7-b6f54dc38a41", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Pork Stew has good nutritional quality for work out", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 2, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Lunch", "Color": "#507FF7"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Grill Chicken", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "8a14332d-9791-41f8-8f31-8e46c8c5020c", "Steps": [{"Name": "Getting start", "CookTime": "00:10:00", "Cookware": ["Bowl"], "Description": "In a large bowl, whisk the salt in the water to dissolve.\n\nAdd the chicken breasts to the brine.\n\nPut in the refrigerator and chill for 30 minutes.", "Ingredients": ["Salt", "Water", "Chicken"], "Number": 1, "UrlVideo": ""}, {"Name": "First step", "CookTime": "00:10:00", "Cookware": ["Grill", "Pan"], "Description": "Arrange your grill so that one side is for high direct heat, and the other side is cooler.\n\nAlternatively, you can use a grill pan, set over medium-high heat.\n\nRemove chicken breasts from brine and pat dry.\n\nCoat with olive oil, and sprinkle evenly with paprika.", "Ingredients": ["Chicken", "Olive oil", "<PERSON><PERSON><PERSON>"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:20:00", "Cookware": ["Grill", "Pan"], "Description": "Brush some olive oil on the grill grates.\n\nPlace chicken breasts on the hot side of the grill (or on the grill pan).\n\nLet the chicken grill, undisturbed, until the pieces start getting some grill marks (you can lift up one to check).\n\nWhen the chicken pieces have browned on one side, turn them over, and move them to the cooler side of the grill (low heat, not no heat).\n\nCover, and let them finish cooking.", "Ingredients": ["Olive oil", "Chicken"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/grill_chicken.png", "Serves": 1, "CookTime": "00:40:00", "Difficulty": 1, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/olive.png", "Name": "Olive oil", "Quantity": "20 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/chicken.png", "Name": "Chicken", "Quantity": "900 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "946 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt", "Quantity": "33 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/hot_pepper.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "15 gr"}], "Calories": "525 kcal", "Reviews": [{"Id": "38bf80d7-c477-4d6b-ab38-cb172e98267b", "RecipeId": "8a14332d-9791-41f8-8f31-8e46c8c5020c", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Grill Chicken tasty meal to get out of the simple chicken recipe.", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 2, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Lunch", "Color": "#507FF7"}, "Date": "2022-10-15T00:00:00Z", "Save": false}]}, {"Id": "7530095a-826f-413e-9e98-43e7b8412f08", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Name": "Easy", "PinsNumber": 4, "Recipes": [{"Name": "Fresh Salad Pasta", "UserId": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Id": "b9e6502b-b373-4fcf-86b2-89379667aa5c", "Steps": [{"Name": "Getting started", "CookTime": "00:12:00", "Cookware": ["Pot"], "Description": "Bring a large pot of lightly salted water to a boil.\n\nCook pasta in the boiling water, stirring occasionally, until tender yet firm to the bite, about 10 to 12 minutes; rinse under cold water and drain.", "Ingredients": ["Pasta"], "Number": 1, "UrlVideo": ""}, {"Name": "Next step", "CookTime": "00:02:00", "Cookware": ["<PERSON><PERSON>"], "Description": "Whisk Italian dressing and salad spice mix together until smooth", "Ingredients": ["Spices"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:01:00", "Cookware": ["Salad bowl"], "Description": "Combine pasta, tomatoes, bell peppers, and olives in a salad bowl; pour dressing over salad and toss to coat.\n\nRefrigerate salad, 8 hours to overnight.", "Ingredients": ["Tomatoes", "Pasta", "Bell Peppers", "Olives"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/fresh_salad_pasta.png", "Serves": 1, "CookTime": "00:25:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt and pepper", "Quantity": "30 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/tomato.png", "Name": "Tomatoes", "Quantity": "100 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/olive.png", "Name": "Olives", "Quantity": "50 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/spaghetti.png", "Name": "Pasta", "Quantity": "50 g"}], "Calories": "350 kcal", "Reviews": [{"Id": "6a0fa592-1b1d-4855-ba83-7f6fd0120830", "RecipeId": "b9e6502b-b373-4fcf-86b2-89379667aa5c", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}, {"Id": "60d799c0-804e-4f48-9ad0-5a4def0bcbd2", "RecipeId": "b9e6502b-b373-4fcf-86b2-89379667aa5c", "PublisherName": "<PERSON>", "UrlAuthorImage": "ms-appx:///Assets/Profiles/james_wolden.png", "CreatedBy": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Healthy and tasty", "Creator": {"Id": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "UrlProfileImage": "ms-appx:///Assets/Profiles/troyan_smith.png", "FullName": "<PERSON><PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 147, "Following": 259, "Recipes": 45}, "Category": {"Id": 3, "UrlIcon": "ms-appx:///Assets/Icons/fork_and_knife_with_plate.png", "Name": "Dinner", "Color": "#F16583"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Circle Cake", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "1dc0e330-ab16-43cc-9b84-fdaf2c5e83cd", "Steps": [{"Name": "Getting started", "CookTime": "00:02:00", "Cookware": ["Bowl", "<PERSON>poon", "<PERSON><PERSON>", "Oven"], "Description": "Position a rack in the middle of the oven and preheat to 350 degrees.\n\nWhile the oven is heating, combine the flour, baking powder, and salt in a bowl, mixing well.", "Ingredients": ["Flour", "Baking powder", "Salt"], "Number": 1, "UrlVideo": ""}, {"Name": "First step", "CookTime": "00:05:00", "Cookware": ["Bowl", "Mixer"], "Description": "Place the butter and sugar in the bowl of a heavy-duty mixer fitted with the paddle attachment and beat on medium speed for about 5 minutes, or until very soft and light.\n\nBeat in the vanilla.", "Ingredients": ["Butter", "Sugar", "Vanilla"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:30:00", "Cookware": ["Toothpick", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "Description": "Bake the layers for about 30 to 35 minutes, until they are well risen and firm and a toothpick inserted in the center emerges clean.\n\nCool the layers in the pans on racks for 5 minutes, then unmold onto racks to finish cooling right side up.", "Ingredients": ["Butter"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/circle_cake.png", "Serves": 1, "CookTime": "00:37:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/butter.png", "Name": "Butter", "Quantity": "500 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "50 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/flour.png", "Name": "Flour", "Quantity": "1000 gr"}], "Calories": "550 kcal", "Reviews": [{"Id": "9de1691e-da43-4788-8a98-0e5b2f2a2a6c", "RecipeId": "1dc0e330-ab16-43cc-9b84-fdaf2c5e83cd", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}, {"Id": "8a8e8398-049f-466c-8f7e-634c30815cd8", "RecipeId": "1dc0e330-ab16-43cc-9b84-fdaf2c5e83cd", "PublisherName": "<PERSON>", "UrlAuthorImage": "ms-appx:///Assets/Profiles/james_wolden.png", "CreatedBy": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Impressive with a very fancy form", "Creator": {"Id": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "UrlProfileImage": "ms-appx:///Assets/Profiles/troyan_smith.png", "FullName": "<PERSON><PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 147, "Following": 259, "Recipes": 45}, "Category": {"Id": 4, "UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Snack", "Color": "#CAC2FC"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Mom's Cheesecake", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "f89a5305-4d77-4f66-a97c-65adba6b36ff", "Steps": [{"Name": "Getting started", "CookTime": "00:11:00", "Cookware": ["<PERSON><PERSON>", "Bowl"], "Description": "In the bowl of a stand mixer or in a large bowl (using a hand mixer) add cream cheese and stir until smooth and creamy (don’t over-beat or you’ll incorporate too much air).\n\nAdd sugar and stir again until creamy.", "Ingredients": ["Sugar", "Cream Cheese"], "Number": 1, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:01:00", "Cookware": ["<PERSON><PERSON>", "Spa<PERSON>la"], "Description": "Add sour cream, vanilla extract, and salt, and stir until well-combined.\n\nIf using a stand mixer, make sure you pause periodically to scrape the sides and bottom of the bowl with a spatula so that all ingredients are evenly incorporated", "Ingredients": ["Sour cream, Vanilla extract"], "Number": 3, "UrlVideo": ""}, {"Name": "Third step", "CookTime": "00:02:00", "Cookware": ["Spa<PERSON>la", "Bowl"], "Description": "With mixer on low speed, gradually add lightly beaten eggs, one at a time, stirring just until each egg is just incorporated.\n\nOnce all eggs have been added, use a spatula to scrape the sides and bottom of the bowl again and make sure all ingredients are well combined.", "Ingredients": ["Eggs"], "Number": 4, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "01:25:00", "Cookware": ["Oven", "<PERSON><PERSON>", "<PERSON><PERSON>", "Springform pan"], "Description": "Transfer to the center rack of your oven and bake on 325F (160C) for about 75 minutes.\n\nEdges will likely have slightly puffed and may have just begun to turn a light golden brown and the center should spring back to the touch but will still be Jello-jiggly.\n\nDon't over-bake or the texture will suffer, which means we all suffer.\n\nRemove from oven and allow to cool on top of the oven³ for 10 minutes.\n\nOnce 10 minutes has passed, use a knife to gently loosen the crust from the inside of the springform pan (this will help prevent cracks as your cheesecake cools and shrinks).", "Ingredients": ["Jello-jiggly", "Cream cheese"], "Number": 5, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/moms_cheesecake.png", "Serves": 1, "CookTime": "01:38:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/egg.png", "Name": "Eggs", "Quantity": "30 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/cheese.png", "Name": "Cream cheese", "Quantity": "100 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "50 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/jello.png", "Name": "Jello-jiggly", "Quantity": "50 g"}], "Calories": "700 kcal", "Reviews": [{"Id": "5dad6dce-339c-4af1-b99c-f530946a8a1e", "RecipeId": "f89a5305-4d77-4f66-a97c-65adba6b36ff", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Awesome as a mom gift", "Creator": {"Id": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "UrlProfileImage": "ms-appx:///Assets/Profiles/troyan_smith.png", "FullName": "<PERSON><PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 147, "Following": 259, "Recipes": 45}, "Category": {"Id": 1, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Breakfast", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": true}, {"Name": "<PERSON><PERSON><PERSON>", "UserId": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Id": "4761a59b-80bc-4910-b443-8ecc74565719", "Steps": [{"Name": "Getting started", "CookTime": "00:05:00", "Cookware": ["Bowl", "<PERSON>poon"], "Description": "Measure flour into a large mixing bowl.\n\nSlowly whisk in milk.\n\nWhisk in eggs, sugar, vanilla extract, cinnamon, and salt until smooth.", "Ingredients": ["Flour", "Milk", "Eggs", "Sugar", "Vanilla extract", "Cinnamon", "Salt"], "Number": 1, "UrlVideo": ""}, {"Name": "Second step", "CookTime": "00:23:00", "Cookware": ["Bowl", "Oiled griddle", "Frying pan"], "Description": "Heat a lightly oiled griddle or frying pan over medium heat.\n\nSoak bread slices in milk mixture until saturated.", "Ingredients": ["Milk", "Eggs", "Vanilla"], "Number": 2, "UrlVideo": ""}, {"Name": "Final step", "CookTime": "00:10:00", "Cookware": ["Batches", "Griddle", "<PERSON><PERSON><PERSON>"], "Description": "Working in batches, cook bread on the preheated griddle or pan until golden brown on each side.\n\nServe hot.", "Ingredients": ["Bread"], "Number": 3, "UrlVideo": ""}], "ImageUrl": "ms-appx:///Assets/Recipes/fluffy_french_toast.png", "Serves": 1, "CookTime": "00:38:00", "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/egg.png", "Name": "Eggs", "Quantity": "3"}, {"UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Bread", "Quantity": "50 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/milk.png", "Name": "Milk", "Quantity": "200 ml"}], "Calories": "400 kcal", "Reviews": [{"Id": "6b07573b-cf63-420a-937b-dd004682523b", "RecipeId": "4761a59b-80bc-4910-b443-8ecc74565719", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Good to have a pretty nice breakfast", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 1, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Breakfast", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": true}]}]