{"openapi": "3.0.1", "info": {"title": "Analytics & Financial API", "version": "v1", "description": "API for analytics, payments, and referrals in Omani Job Transfer Application"}, "servers": [{"url": "https://localhost:5002", "description": "Local development server"}], "paths": {"/api/analytics": {"get": {"summary": "Get platform analytics", "operationId": "GetAnalytics", "tags": ["Analytics"], "responses": {"200": {"description": "Platform analytics data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Analytics"}}}}}}}, "/api/payment/user/{userId}/transactions": {"get": {"summary": "Get user payment transactions", "operationId": "GetUserPaymentTransactions", "tags": ["Payment"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User payment transactions", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentTransaction"}}}}}}}}, "/api/referral/user/{userId}": {"get": {"summary": "Get user referrals", "operationId": "GetUserReferrals", "tags": ["Referral"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User referrals", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Referral"}}}}}}}}}, "components": {"schemas": {"Analytics": {"type": "object", "properties": {"totalUsers": {"type": "integer"}, "activeUsers": {"type": "integer"}, "totalTransferRequests": {"type": "integer"}, "successfulTransfers": {"type": "integer"}, "totalRevenue": {"type": "number", "format": "decimal"}, "subscriptionRevenue": {"type": "number", "format": "decimal"}, "promotionRevenue": {"type": "number", "format": "decimal"}, "userGrowthRate": {"type": "number", "format": "decimal"}, "averageSessionDuration": {"type": "number", "format": "decimal"}, "topJobCategories": {"type": "array", "items": {"type": "object", "properties": {"category": {"type": "string"}, "count": {"type": "integer"}}}}}}, "PaymentTransaction": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string", "format": "uuid"}, "amount": {"type": "number", "format": "decimal"}, "currency": {"type": "string"}, "type": {"type": "string", "enum": ["subscription", "token_purchase", "promotion_package", "premium_feature"]}, "status": {"type": "string", "enum": ["pending", "completed", "failed", "refunded"]}, "paymentMethod": {"type": "string"}, "transactionDate": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "referenceId": {"type": "string"}}, "required": ["id", "userId", "amount", "type"]}, "Referral": {"type": "object", "properties": {"id": {"type": "string"}, "referrerId": {"type": "string", "format": "uuid"}, "referredUserId": {"type": "string", "format": "uuid"}, "referralCode": {"type": "string"}, "status": {"type": "string", "enum": ["pending", "completed", "rewarded"]}, "rewardAmount": {"type": "number", "format": "decimal"}, "createdAt": {"type": "string", "format": "date-time"}, "completedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "referrerId", "referralCode"]}}}}