[{"Name": "Avocado Toast", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "0dc51562-67b7-4de8-91fa-a0a4a538d919", "Steps": [{"Name": "Getting started", "CookTime": {"ticks": 3000000000}, "Cookware": ["Fork", "Knife", "<PERSON><PERSON><PERSON>", "Spa<PERSON>la"], "Description": "Melt butter in a skillet over medium-low heat.\n\nCrack eggs into the skillet side by side and cook until eggs are white on the bottom layer and firm enough to flip, 2 to 3 minutes.", "Ingredients": ["Eggs", "Olive oil"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Next step", "CookTime": {"ticks": 3000000000}, "Cookware": ["<PERSON><PERSON><PERSON>", "Spa<PERSON>la"], "Description": "Flip eggs, trying not to crack the yolk, and cook until the egg reaches desired doneness, 2 to 4 minutes more.", "Ingredients": ["Bread"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 3000000000}, "Cookware": ["Fork", "Knife", "Bowl", "Cutting Boards"], "Description": "Meanwhile, toast bread slices to desired doneness, 3 to 4 minutes.", "Ingredients": ["Avocado", "Lemon juice", "Salt and pepper"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/avocado_toast.png", "Serves": 1, "CookTime": {"ticks": 9600000000}, "Difficulty": 1, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/avocado.png", "Name": "Avocado", "Quantity": "250 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/egg.png", "Name": "Eggs", "Quantity": "2"}, {"UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Bread", "Quantity": "300 g"}, {"UrlIcon": "ms-appx:///Assets/Icons/lemon.png", "Name": "Lemon juice", "Quantity": "80 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/olive.png", "Name": "Olive oil", "Quantity": "50 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt and pepper", "Quantity": "salt to taste"}], "Calories": "250 kcal", "Reviews": [], "Details": "Details", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 1, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Breakfast", "Color": "#7A67F8"}, "Date": "2022-10-18T00:00:00Z", "Save": true}, {"Name": "Fresh Salad Thaid", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "0007e65f-3d5c-4ca3-842f-134b7f4a8b52", "Steps": [{"Name": "Getting started", "CookTime": {"ticks": 1800000000}, "Cookware": ["Knife", "Dish"], "Description": "Cut all your vegetables to size.\n\nYou can also use a bag of pre-shredded coleslaw.\n\nI diced the English cucumbers and cut the red bell peppers into thin strips.\n\nYou may also use julienne carrots, lettuce, etc.", "Ingredients": ["Carrot"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "CookTime": {"ticks": 3600000000}, "Cookware": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Knife", "Cutting Boards"], "Description": "Place the dressing ingredients in a jar and shake together until well combined.\n\nAdd the mix salad greens first, then strategically place the carrot, onion, red pepper and cucumber around the mixed greens.", "Ingredients": ["<PERSON><PERSON>", "Carrot", "Onion", "Red Pepper", "<PERSON><PERSON><PERSON>ber"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 600000000}, "Cookware": ["Fork", "Knife", "Bowl"], "Description": "Finish up with the dressing.\n\nAdd the dressing just before you want to serve the salad otherwise it'll go soggy.", "Ingredients": ["Lime Juice", "Sesame Oil", "<PERSON>"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/fresh_salad_thaid.png", "Serves": 1, "CookTime": {"ticks": **********}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/lemon.png", "Name": "Lime Juice", "Quantity": "80 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/cucumber.png", "Name": "<PERSON><PERSON><PERSON>ber", "Quantity": "50 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/carrot.png", "Name": "Carrot", "Quantity": "50 g"}], "Calories": "350 kcal", "Reviews": [{"Id": "bd95eeef-643e-4eb0-bbd8-1456791040f8", "RecipeId": "0007e65f-3d5c-4ca3-842f-134b7f4a8b52", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}, {"Id": "6f597075-773c-4160-bf75-77054fee55cf", "RecipeId": "0007e65f-3d5c-4ca3-842f-134b7f4a8b52", "PublisherName": "<PERSON>", "UrlAuthorImage": "ms-appx:///Assets/Profiles/james_wolden.png", "CreatedBy": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Fresh, healthy and tasty", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 2, "UrlIcon": "ms-appx:///Assets/Icons/hamburger.png", "Name": "Lunch", "Color": "#507FF7"}, "Date": "2022-10-18T00:00:00Z", "Save": false}, {"Name": "Salmon <PERSON>", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "5ad60d30-2891-4c4b-99a7-e138e6477191", "Steps": [{"Name": "Getting started", "CookTime": {"ticks": **********}, "Cookware": ["Knife", "Dish", "<PERSON><PERSON><PERSON>"], "Description": "Whenever I have fresh spinach in the house, I love wilting it into the sauce during the final few minutes of cooking to add some greens to this dish.", "Ingredients": ["<PERSON><PERSON>"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "CookTime": {"ticks": **********}, "Cookware": ["Knife", "Dish", "<PERSON><PERSON><PERSON>"], "Description": "If you happy to have any other Italian herbs in the house (rosemary, oregano, thyme, etc), feel free to add them in to taste", "Ingredients": ["<PERSON>", "Oregano"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Third step", "CookTime": {"ticks": **********}, "Cookware": ["<PERSON>poon", "Dish", "Knife"], "Description": "Instead of (or — if you’re feeling extra-indulgent — in addition to) the heavy cream, add in a torn ball of fresh burrata to the dish just before serving.\n\nI can vouch that it is heavenly.", "Ingredients": ["Cream", "Fresh burrata"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 600000000}, "Cookware": ["Wooden spoon"], "Description": "I have to admit that I really prefer the texture/flavor of this dish using fresh tomatoes.\n\nBut if you’re in a pinch, it will also taste great using one large (28-ounce) can of good-quality whole tomatoes, which you can break up with a wooden spoon as they cook.", "Ingredients": ["Tomatoes", "Sesame Oil", "<PERSON>"], "Number": 4, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/salmon_tomato_sauce.png", "Serves": 1, "CookTime": {"ticks": 6600000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/leafy_green.png", "Name": "<PERSON><PERSON>", "Quantity": "200 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/hot_pepper.png", "Name": "<PERSON>", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/tomato.png", "Name": "Tomatoes", "Quantity": "100 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/butter.png", "Name": "Fresh burrata", "Quantity": "50 g"}], "Calories": "200 kcal", "Reviews": [{"Id": "9702c946-c6f0-4f18-91f0-a959990bb306", "RecipeId": "5ad60d30-2891-4c4b-99a7-e138e6477191", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}, {"Id": "2689c8d7-fd91-4495-95e3-9a11e6085473", "RecipeId": "5ad60d30-2891-4c4b-99a7-e138e6477191", "PublisherName": "<PERSON>", "UrlAuthorImage": "ms-appx:///Assets/Profiles/james_wolden.png", "CreatedBy": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Nutritious and great after workout.", "Creator": {"Id": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "UrlProfileImage": "ms-appx:///Assets/Profiles/troyan_smith.png", "FullName": "<PERSON><PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 147, "Following": 259, "Recipes": 45}, "Category": {"Id": 2, "UrlIcon": "ms-appx:///Assets/Icons/hamburger.png", "Name": "Lunch", "Color": "#507FF7"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Fresh Salad Pasta", "UserId": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Id": "b9e6502b-b373-4fcf-86b2-89379667aa5c", "Steps": [{"Name": "Getting started", "CookTime": {"ticks": 7200000000}, "Cookware": ["Pot"], "Description": "Bring a large pot of lightly salted water to a boil.\n\nCook pasta in the boiling water, stirring occasionally, until tender yet firm to the bite, about 10 to 12 minutes; rinse under cold water and drain.", "Ingredients": ["Pasta"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Next step", "CookTime": {"ticks": **********}, "Cookware": ["<PERSON><PERSON>"], "Description": "Whisk Italian dressing and salad spice mix together until smooth", "Ingredients": ["Spices"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 600000000}, "Cookware": ["Salad bowl"], "Description": "Combine pasta, tomatoes, bell peppers, and olives in a salad bowl; pour dressing over salad and toss to coat.\n\nRefrigerate salad, 8 hours to overnight.", "Ingredients": ["Tomatoes", "Pasta", "Bell Peppers", "Olives"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/fresh_salad_pasta.png", "Serves": 1, "CookTime": {"ticks": 15000000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt and pepper", "Quantity": "30 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/tomato.png", "Name": "Tomatoes", "Quantity": "100 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/olive.png", "Name": "Olives", "Quantity": "50 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/spaghetti.png", "Name": "Pasta", "Quantity": "50 g"}], "Calories": "350 kcal", "Reviews": [{"Id": "6a0fa592-1b1d-4855-ba83-7f6fd0120830", "RecipeId": "b9e6502b-b373-4fcf-86b2-89379667aa5c", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}, {"Id": "60d799c0-804e-4f48-9ad0-5a4def0bcbd2", "RecipeId": "b9e6502b-b373-4fcf-86b2-89379667aa5c", "PublisherName": "<PERSON>", "UrlAuthorImage": "ms-appx:///Assets/Profiles/james_wolden.png", "CreatedBy": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Healthy and tasty", "Creator": {"Id": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "UrlProfileImage": "ms-appx:///Assets/Profiles/troyan_smith.png", "FullName": "<PERSON><PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 147, "Following": 259, "Recipes": 45}, "Category": {"Id": 3, "UrlIcon": "ms-appx:///Assets/Icons/fork_and_knife_with_plate.png", "Name": "Dinner", "Color": "#F16583"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Circle Cake", "UserId": "bed8ff9c-08f7-4c44-b5a3-4d95708f245e", "Id": "1dc0e330-ab16-43cc-9b84-fdaf2c5e83cd", "Steps": [{"Name": "Getting started", "CookTime": {"ticks": **********}, "Cookware": ["Bowl", "<PERSON>poon", "<PERSON><PERSON>", "Oven"], "Description": "Position a rack in the middle of the oven and preheat to 350 degrees.\n\nWhile the oven is heating, combine the flour, baking powder, and salt in a bowl, mixing well.", "Ingredients": ["Flour", "Baking powder", "Salt"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": 3000000000}, "Cookware": ["Bowl", "Mixer"], "Description": "Place the butter and sugar in the bowl of a heavy-duty mixer fitted with the paddle attachment and beat on medium speed for about 5 minutes, or until very soft and light.\n\nBeat in the vanilla.", "Ingredients": ["Butter", "Sugar", "Vanilla"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 18000000000}, "Cookware": ["Toothpick", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "Description": "Bake the layers for about 30 to 35 minutes, until they are well risen and firm and a toothpick inserted in the center emerges clean.\n\nCool the layers in the pans on racks for 5 minutes, then unmold onto racks to finish cooling right side up.", "Ingredients": ["Butter"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/circle_cake.png", "Serves": 1, "CookTime": {"ticks": 22200000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/butter.png", "Name": "Butter", "Quantity": "500 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "50 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/flour.png", "Name": "Flour", "Quantity": "1000 gr"}], "Calories": "550 kcal", "Reviews": [{"Id": "9de1691e-da43-4788-8a98-0e5b2f2a2a6c", "RecipeId": "1dc0e330-ab16-43cc-9b84-fdaf2c5e83cd", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}, {"Id": "8a8e8398-049f-466c-8f7e-634c30815cd8", "RecipeId": "1dc0e330-ab16-43cc-9b84-fdaf2c5e83cd", "PublisherName": "<PERSON>", "UrlAuthorImage": "ms-appx:///Assets/Profiles/james_wolden.png", "CreatedBy": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Impressive with a very fancy form", "Creator": {"Id": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "UrlProfileImage": "ms-appx:///Assets/Profiles/troyan_smith.png", "FullName": "<PERSON><PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 147, "Following": 259, "Recipes": 45}, "Category": {"Id": 4, "UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Snack", "Color": "#CAC2FC"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Mom's Cheesecake", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "f89a5305-4d77-4f66-a97c-65adba6b36ff", "Steps": [{"Name": "Getting started", "CookTime": {"ticks": 6600000000}, "Cookware": ["<PERSON><PERSON>", "Bowl"], "Description": "In the bowl of a stand mixer or in a large bowl (using a hand mixer) add cream cheese and stir until smooth and creamy (don’t over-beat or you’ll incorporate too much air).\n\nAdd sugar and stir again until creamy.", "Ingredients": ["Sugar", "Cream Cheese"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "CookTime": {"ticks": 600000000}, "Cookware": ["<PERSON><PERSON>", "Spa<PERSON>la"], "Description": "Add sour cream, vanilla extract, and salt, and stir until well-combined.\n\nIf using a stand mixer, make sure you pause periodically to scrape the sides and bottom of the bowl with a spatula so that all ingredients are evenly incorporated", "Ingredients": ["Sour cream, Vanilla extract"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Third step", "CookTime": {"ticks": **********}, "Cookware": ["Spa<PERSON>la", "Bowl"], "Description": "With mixer on low speed, gradually add lightly beaten eggs, one at a time, stirring just until each egg is just incorporated.\n\nOnce all eggs have been added, use a spatula to scrape the sides and bottom of the bowl again and make sure all ingredients are well combined.", "Ingredients": ["Eggs"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 51000000000}, "Cookware": ["Oven", "<PERSON><PERSON>", "<PERSON><PERSON>", "Springform pan"], "Description": "Transfer to the center rack of your oven and bake on 325F (160C) for about 75 minutes.\n\nEdges will likely have slightly puffed and may have just begun to turn a light golden brown and the center should spring back to the touch but will still be Jello-jiggly.\n\nDon't over-bake or the texture will suffer, which means we all suffer.\n\nRemove from oven and allow to cool on top of the oven³ for 10 minutes.\n\nOnce 10 minutes has passed, use a knife to gently loosen the crust from the inside of the springform pan (this will help prevent cracks as your cheesecake cools and shrinks).", "Ingredients": ["Jello-jiggly", "Cream cheese"], "Number": 4, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/moms_cheesecake.png", "Serves": 1, "CookTime": {"ticks": 58800000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/egg.png", "Name": "Eggs", "Quantity": "30 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/cheese.png", "Name": "Cream cheese", "Quantity": "100 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "50 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/jello.png", "Name": "Jello-jiggly", "Quantity": "50 g"}], "Calories": "700 kcal", "Reviews": [{"Id": "5dad6dce-339c-4af1-b99c-f530946a8a1e", "RecipeId": "f89a5305-4d77-4f66-a97c-65adba6b36ff", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Awesome as a mom gift", "Creator": {"Id": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "UrlProfileImage": "ms-appx:///Assets/Profiles/troyan_smith.png", "FullName": "<PERSON><PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 147, "Following": 259, "Recipes": 45}, "Category": {"Id": 1, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Breakfast", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": true}, {"Name": "<PERSON><PERSON><PERSON>", "UserId": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Id": "4761a59b-80bc-4910-b443-8ecc74565719", "Steps": [{"Name": "Getting started", "CookTime": {"ticks": 3000000000}, "Cookware": ["Bowl", "<PERSON>poon"], "Description": "Measure flour into a large mixing bowl.\n\nSlowly whisk in milk.\n\nWhisk in eggs, sugar, vanilla extract, cinnamon, and salt until smooth.", "Ingredients": ["Flour", "Milk", "Eggs", "Sugar", "Vanilla extract", "Cinnamon", "Salt"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "CookTime": {"ticks": 13800000000}, "Cookware": ["Bowl", "Oiled griddle", "Frying pan"], "Description": "Heat a lightly oiled griddle or frying pan over medium heat.\n\nSoak bread slices in milk mixture until saturated.", "Ingredients": ["Milk", "Eggs", "Vanilla"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": **********}, "Cookware": ["Batches", "Griddle", "<PERSON><PERSON><PERSON>"], "Description": "Working in batches, cook bread on the preheated griddle or pan until golden brown on each side.\n\nServe hot.", "Ingredients": ["Bread"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/fluffy_french_toast.png", "Serves": 1, "CookTime": {"ticks": 22800000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/egg.png", "Name": "Eggs", "Quantity": "3"}, {"UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Bread", "Quantity": "50 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/milk.png", "Name": "Milk", "Quantity": "200 ml"}], "Calories": "400 kcal", "Reviews": [{"Id": "6b07573b-cf63-420a-937b-dd004682523b", "RecipeId": "4761a59b-80bc-4910-b443-8ecc74565719", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Good to have a pretty nice breakfast", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 1, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Breakfast", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": true}, {"Name": "Super Duper Oatmeal", "UserId": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "Id": "63e275f8-9222-48b2-83cb-df6c0961a1eb", "Steps": [{"Name": "Getting started", "CookTime": {"ticks": 3000000000}, "Cookware": ["Bowl"], "Description": "Bring the milk and water to a boil in a bowl.\n\nCombine flours, oats, dried cranberries and apricots, baking powder, and salt in a large mixing bowl.", "Ingredients": ["Water", "Milk", "Oats", "Cranberries", "Apricots", "Salt"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "CookTime": {"ticks": 600000000}, "Cookware": ["Pot", "<PERSON>poon"], "Description": "Mix in the oats, and reduce heat to medium.\n\nStir in blueberries, applesauce, wheat germ, cinnamon, and sugar.", "Ingredients": ["Oats", "Blueberries", "Applesauce", "Wheat germ", "Cinnamon", "Sugar"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 5400000000}, "Cookware": ["Pot"], "Description": "Cook 8 to 10 minutes, or until oats are tender.\n\nAs they finish, put them on a plate and hold in the oven while cooking remaining oats.", "Ingredients": ["Oats"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/super_duper_oatmeal.png", "Serves": 1, "CookTime": {"ticks": **********}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/blueberries.png", "Name": "Blueberries", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/green_apple.png", "Name": "Applesauce", "Quantity": "50 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/flour.png", "Name": "Oats", "Quantity": "50 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "300 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "400 gr"}], "Calories": "150 kcal", "Reviews": [{"Id": "b79797c0-5255-4937-ac02-4df5f231c8ce", "RecipeId": "63e275f8-9222-48b2-83cb-df6c0961a1eb", "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png", "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "PublisherName": "<PERSON><PERSON>", "Date": "2022-07-12T00:00:00Z", "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Very special dessert to taste.", "Creator": {"Id": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "UrlProfileImage": "ms-appx:///Assets/Profiles/troyan_smith.png", "FullName": "<PERSON><PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 147, "Following": 259, "Recipes": 45}, "Category": {"Id": 1, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Breakfast", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Walnut and nuts", "UserId": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Id": "f649eefe-93cb-4bca-93a2-af707966d217", "Steps": [{"Name": "Getting started", "CookTime": {"ticks": **********}, "Cookware": ["Bowl"], "Description": "In a bowl, combine flour and sugar.\n\nCut in butter until mixture resembles coarse crumbs.\n\nCombine egg yolks and milk; stir into flour mixture until blended.\n\nWith lightly floured hands, press dough onto the bottom and 1 in.", "Ingredients": ["Flour", "Sugar", "Eggs", "Milk"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second Step", "CookTime": {"ticks": **********}, "Cookware": ["Knife", "Baking Sheet", "Pan"], "Description": "Up the sides of a 12-in.\n\nTart pan with removable bottom.\n\nLine unpricked crust with a double thickness of heavy-duty foil.\n\nFill with pie weights.\n\nPlace pan on a baking sheet.\n\nBake at 375° until edges are lightly browned, 12-15 minutes.", "Ingredients": ["Eggs", "Milk"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": **********0}, "Cookware": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>poon", "Wire rack"], "Description": "Meanwhile, in a saucepan, combine the sugar, cream, cinnamon and salt.\n\nBring to a boil over medium heat, stirring constantly.\n\nRemove from the heat; stir in walnuts.\n\nRemove foil from crust; pour filling into crust.\n\nBake until golden brown, 20-25 minutes.\n\nCool on a wire rack.\n\nStore in the refrigerator.", "Ingredients": ["Sugar", "Cream", "Cinnamon", "Salt", "Peanuts"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/walnut_and_nuts.png", "Serves": 1, "CookTime": {"ticks": 19200000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/peanuts.png", "Name": "Peanuts", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/egg.png", "Name": "Eggs", "Quantity": "5"}, {"UrlIcon": "ms-appx:///Assets/Icons/green_apple.png", "Name": "Applesauce", "Quantity": "300 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt", "Quantity": "30 gr"}], "Calories": "150 kcal", "Reviews": [{"Id": "92ebde78-0049-4e8c-af6f-4e7dc6d6c0b2", "RecipeId": "f649eefe-93cb-4bca-93a2-af707966d217", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Walnuts recipe to get very taste dessert", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 1, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Breakfast", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Avocado Salad", "UserId": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Id": "f649eefe-93cb-4bca-93a2-af806966d217", "Steps": [{"Name": "Getting started", "CookTime": {"ticks": 3000000000}, "Cookware": ["Mixing bowl"], "Description": "In a small mixing bowl whisk together lemon juice, red wine vinegar, extra virgin oil oil, honey, garlic.", "Ingredients": ["Lemon juic", "Red wine vinegar", "Extra virgin oil oil", "Honey", "<PERSON><PERSON><PERSON>"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "CookTime": {"ticks": **********}, "Cookware": ["Mixing bowl"], "Description": "After whisk, to get better taste pour cilantro, parsley, oregano, and season with salt and pepper.", "Ingredients": ["Cilantro", "<PERSON><PERSON><PERSON>", "Oregano", "Salt", "Pepper"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": **********}, "Cookware": ["Bowl"], "Description": "In a large bowl gently toss together cucumbers, tomatoes, red onion, avocado with dressing.", "Ingredients": ["Cucumbers", "Tomatoes", "Red onion", "Avocado"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/avocado_salad.png", "Serves": 1, "CookTime": {"ticks": 15000000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/lemon.png", "Name": "Lemon juice", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/olive.png", "Name": "Red wine vinegar", "Quantity": "10 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/honey.png", "Name": "Honey", "Quantity": "7 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/garlic.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "1"}, {"UrlIcon": "ms-appx:///Assets/Icons/parsley.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "3 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt and pepper", "Quantity": "3 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/cucumber.png", "Name": "Cucumbers", "Quantity": "5 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/tomato.png", "Name": "Tomatoes", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/onion.png", "Name": "Red onion", "Quantity": "4 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/avocado.png", "Name": "Avocado", "Quantity": "10 gr"}], "Calories": "200 kcal", "Reviews": [{"Id": "0d191b8a-993c-47b0-908c-30c5bc57980e", "RecipeId": "f649eefe-93cb-4bca-93a2-af806966d217", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Avocado salad recipe is a very healthy meal to loss weight", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 2, "UrlIcon": "ms-appx:///Assets/Icons/hamburger.png", "Name": "Lunch", "Color": "#507FF7"}, "Date": "2022-10-18T00:00:00Z", "Save": false}, {"Name": "Croissants", "UserId": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Id": "f649eefe-57cb-4bca-93a2-af707966d217", "Steps": [{"Name": "Getting started", "CookTime": {"ticks": **********0}, "Cookware": ["Bowl", "Mixer", "<PERSON>h hook"], "Description": "Twenty-four hours before serving, start the détrempe: In the bowl of a stand mixer fitted with the dough hook, combine the flour, sugar, salt and yeast, and stir to combine.", "Ingredients": ["Flour", "Sugar", "Salt", "Honey", "<PERSON><PERSON><PERSON>", "Yeast"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "CookTime": {"ticks": **********}, "Cookware": ["Mixer", "<PERSON>h hook", "<PERSON><PERSON><PERSON>"], "Description": "Create a well in the center, and pour in the water and milk.\n\nMix on low speed until a tight, smooth dough comes together around the hook, about 5 minutes.\n\nRemove the hook and cover the bowl with a damp towel.\n\nSet aside for 10 minutes.", "Ingredients": ["Water", "Milk"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": **********}, "Cookware": ["<PERSON>h hook", "Mixer", "Bowl"], "Description": "Reattach the dough hook and turn the mixer on medium-low speed.\n\nAdd the butter pieces all at once and continue to mix, scraping down the bowl and hook once or twice, until the dough has formed a very smooth, stretchy ball that is not the least bit sticky, 8 to 10 minutes.", "Ingredients": ["Butter"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/croissants.png", "Serves": 1, "CookTime": {"ticks": 27000000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/butter.png", "Name": "Butter", "Quantity": "20 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/flour.png", "Name": "Flour", "Quantity": "50 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "5 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt", "Quantity": "6 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/honey.png", "Name": "Honey", "Quantity": "7 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/garlic.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "1"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "200 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/milk.png", "Name": "Milk", "Quantity": "150 ml"}], "Calories": "800 kcal", "Reviews": [{"Id": "e902d8b8-3167-44d2-859b-51ec28ab772d", "RecipeId": "f649eefe-57cb-4bca-93a2-af707966d217", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Crossiant recipe to get very taste after lunch", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 1, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Breakfast", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Strawberry Cream Pie", "UserId": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Id": "f649eefe-26cb-4bca-93a2-af707012d237", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": **********}, "Cookware": ["Bowl", "Cups"], "Description": "In a medium bowl, mix together 1 1/2 cups of all-purpose flour and 1/2 cup of cold butter.", "Ingredients": ["Flour", "Butter"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "CookTime": {"ticks": **********0}, "Cookware": ["Pie dish", "Rolling pin"], "Description": "Use a fork or pastry cutter to blend the butter into the flour until it resembles coarse crumbs.\n\nSlowly add in 1/4 cup of ice water, 1 tablespoon at a time, and mix until the dough comes together.\n\nRoll out the dough on a floured surface to fit your pie dish and press it into the bottom and up the sides.\n\nPrick the crust all over with a fork and preheat the oven to 375F.\n\nBake the crust for 15-20 minutes, or until lightly golden brown.", "Ingredients": ["Flour", "Butter", "Water"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 15000000000}, "Cookware": ["Medium saucepan", "Whisk"], "Description": "In a medium saucepan, mix together 1 cup of heavy cream, 1/2 cup of granulated sugar, and 1 teaspoon of vanilla extract.\n\nHeat the mixture over medium heat, whisking constantly, until it comes to a simmer.\n\nIn a small bowl, mix together 2 tablespoons of cornstarch and 2 tablespoons of cold water to make a slurry.\n\nSlowly pour the slurry into the saucepan and continue to whisk until the mixture thickens.\n\nRemove from heat and stir in 2 cups of sliced strawberries.\n\nPour the filling into the cooled crust and refrigerate for at least 2 hours before serving.", "Ingredients": ["Cream", "Sugar", "Vanilla extract", "Cornstarch", "Water", "Strawberries"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/strawberry_cream_pie.png", "Serves": 1, "CookTime": {"ticks": 33000000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/flour.png", "Name": "Flour", "Quantity": "50 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/butter.png", "Name": "Butter", "Quantity": "150 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "200 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "150 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Strawberries", "Quantity": "30 gr"}], "Calories": "300 kcal", "Reviews": [{"Id": "8ca65778-87b7-4417-94c3-8df09ac14dd8", "RecipeId": "f649eefe-26cb-4bca-93a2-af707012d237", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Strawberry Cream Pie recipe is an awusome dessert to eat in any moment", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 1, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Breakfast", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "<PERSON><PERSON><PERSON>", "UserId": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "Id": "f167eefe-78cb-4bca-93a2-af707012d237", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": 3000000000}, "Cookware": ["Bowl"], "Description": "Combine sauce ingredients and stir until brown sugar is dissolved.", "Ingredients": ["Sauce", "Sugar"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "CookTime": {"ticks": **********0}, "Cookware": ["Bowl", "Plastic wrap"], "Description": "Place individual salmon slices in a mixing bowl.\n\nPour the sauce over the salmon, cover with plastic wrap and let marinate 20 minutes (at room temp or refrigerated).", "Ingredients": ["Salmon", "Sauce"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 15000000000}, "Cookware": ["Baking sheet", "Knife"], "Description": "Transfer salmon to prepared baking sheet (keep the marinade).\n\nBake at 400 for 12-16 minutes or until salmon is flaky and cooked through, bake times may vary by thickness and cut of salmon (see notes on how long to bake salmon below).", "Ingredients": ["Salmon"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/teriyaki_salmon.png", "Serves": 1, "CookTime": {"ticks": 30000000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/salmon.png", "Name": "Salmon", "Quantity": "300 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sauce.png", "Name": "Sauce", "Quantity": "100 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "50 mgr"}], "Calories": "350 kcal", "Reviews": [{"Id": "e1674b3f-4833-423f-a90b-472867f22e58", "RecipeId": "f167eefe-78cb-4bca-93a2-af707012d237", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Teriyaki Salmon is a high protein food with lots of nutrients", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 3, "UrlIcon": "ms-appx:///Assets/Icons/fork_and_knife_with_plate.png", "Name": "Dinner", "Color": "#F16583"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Air Fryer French Fries", "UserId": "3f848dba-f972-493a-bf52-0cd93958f4b2", "Id": "f467eefe-67cb-6bca-08a2-af707012d237", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": 3000000000}, "Cookware": ["Potato peeler"], "Description": "We just wash the potatoes and keep the skins on, but you can peel if you prefer.", "Ingredients": ["Potatoes"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": 3000000000}, "Cookware": ["Knife"], "Description": "Clean the potatoes and cut into about 1/4 inch strands, making sure all the strands are the same size for even cooking.\n\nRinse the fries under cold water and pat dry.", "Ingredients": ["Potatoes", "Water"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "CookTime": {"ticks": **********}, "Cookware": ["Bowl", "Air fryer"], "Description": "Add the fries into a bowl and drizzle with oil and toss, season and toss again.\n\nEvenly spread the fries into the air fryer basket.", "Ingredients": ["Potatoes", "Oil"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 3000000000}, "Cookware": ["Bowl", "Air fryer"], "Description": "Cook the fries until crispy and golden, tossing half way.", "Ingredients": ["Potatoes"], "Number": 4, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/air_fryer_french_fries.png", "Serves": 1, "CookTime": {"ticks": 15000000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/potato.png", "Name": "Potatoes", "Quantity": "500 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "400 ml"}], "Calories": "425 kcal", "Reviews": [{"Id": "25370a3f-7bca-4475-9575-3a7b54752e76", "RecipeId": "f467eefe-67cb-6bca-08a2-af707012d237", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Air Fryer French Fries is a delicious plate and more healthy that normal fries", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 4, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Snack", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "<PERSON><PERSON><PERSON> Potatoes", "UserId": "3f848dba-f972-493a-bf52-0cd93958f4b2", "Id": "f907eefe-67cb-6bca-08a2-af356012d237", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": **********}, "Cookware": ["Oven"], "Description": "Preheat the oven to 425ºF", "Ingredients": ["Potatoes"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": **********}, "Cookware": ["Chopstick", "Cutting board", "Knife"], "Description": "Set a potato on a cutting board and place a chopstick on either side of the potato.\n\nWith a sharp, thin knife, make deep vertical cuts 1/8-inch apart, but without cutting all the way through the potato.\n\nThe chopsticks should keep you from accidentally cutting too deeply or going all the way through.", "Ingredients": ["Potatoes"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "CookTime": {"ticks": 3000000000}, "Cookware": ["Baking dish"], "Description": "Place the potatoes with the cut side up in the baking dish, spaced a little apart so each one has some room.\n\nFan the potatoes open slightly.", "Ingredients": ["Potatoes"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 18000000000}, "Cookware": ["Oven"], "Description": "Or until golden and crispy.\n\nThe potatoes will fan out more during cooking and take on their accordion-like appearance.\n\nServe hot.", "Ingredients": ["Potatoes"], "Number": 4, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/hasselback_potatoes.png", "Serves": 1, "CookTime": {"ticks": 33000000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/potato.png", "Name": "Potatoes", "Quantity": "500 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "400 ml"}], "Calories": "375 kcal", "Reviews": [{"Id": "40c710b9-e8bb-40f6-9115-03382aa138fa", "RecipeId": "f907eefe-67cb-6bca-08a2-af356012d237", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Hasselback Potatoes is a delicious plate", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 2, "UrlIcon": "ms-appx:///Assets/Icons/hamburger.png", "Name": "Lunch", "Color": "#507FF7"}, "Date": "2022-10-18T00:00:00Z", "Save": false}, {"Name": "Stir-Fry Chicken and Vegetables", "UserId": "3f848dba-f972-493a-bf52-0cd93958f4b2", "Id": "f907eefe-67cb-6bca-08a2-af716322d237", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": **********}, "Cookware": ["Pan", "Tablespoon"], "Description": "In a large pan on medium-high heat, add 1 tablespoon of oil.\n\nOnce the oil is hot, add chicken, season with salt and pepper, and sauté until cooked through and browned.\n\nRemove cooked chicken from pan and set aside.", "Ingredients": ["Oil", "Chicken", "Salt", "Pepper"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": 3000000000}, "Cookware": ["Pan", "Tablespoon"], "Description": "In the same pan, heat 1 tablespoon of oil and add mushrooms.\n\nWhen the mushrooms start to soften, add broccoli florets and stir-fry until the broccoli is tender.\n\nRemove cooked mushrooms and broccoli from the pan and set aside", "Ingredients": ["<PERSON><PERSON><PERSON><PERSON>"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "CookTime": {"ticks": **********}, "Cookware": ["Pan", "Tablespoon"], "Description": "Add 1 tablespoon of oil to the pan and sauté garlic and ginger until fragrant.\n\nAdd the remaining sauce ingredients and stir until smooth.", "Ingredients": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Sauce"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 4200000000}, "Cookware": ["Pan"], "Description": "Return the chicken and vegetables to the saucy pan, stir until heated through.", "Ingredients": ["Chicken"], "Number": 4, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/stir_fry_chicken_salad.png", "Serves": 1, "CookTime": {"ticks": 19200000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/chicken.png", "Name": "Chicken", "Quantity": "455 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt and pepper", "Quantity": "1 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/broccoli.png", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Quantity": "455 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/garlic.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "3"}, {"UrlIcon": "ms-appx:///Assets/Icons/sauce.png", "Name": "Sauce", "Quantity": "80 ml"}], "Calories": "170 kcal", "Reviews": [{"Id": "d38d9ce4-505b-406a-9f2a-637035f62a95", "RecipeId": "f907eefe-67cb-6bca-08a2-af716322d237", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Stir-Fry Chicken and Vegetables low calories meal", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 2, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Lunch", "Color": "#507FF7"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Spicy Salmon and Vegetables", "UserId": "7f7cddd2-2e1e-4a99-a6e3-88575d2f66f1", "Id": "f907eefe-27cb-6bca-08a2-af356012d237", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": 3000000000}, "Cookware": ["<PERSON><PERSON><PERSON>", "Tablespoon"], "Description": "Preheat the broiler to low heat.\n\nWhisk together the chili garlic sauce, 2 tablespoons of soy sauce, 1 tablespoon of sesame oil, sugar and vinegar until combined.", "Ingredients": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sauce", "Soy", "Oil", "Sugar", "<PERSON><PERSON><PERSON>"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": **********}, "Cookware": ["Bowl", "Cookie sheet", "Non-Stick Foil", "<PERSON><PERSON><PERSON>"], "Description": "Add the cut up salmon to the bowl and mix until coated.\n\nLine a cookie sheet with Non-Stick Foil and evenly spread out the salmon and cook under the broiler for 8 to 10 minutes or until browned and cooked throughout.", "Ingredients": ["Salmon"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "CookTime": {"ticks": **********}, "Cookware": ["Pan", "Tablespoon"], "Description": "Pour the remaining 1 tablespoon of sesame oil into a large frying pan on high heat and add in the bell peppers, snow peas and carrot and stir-fry for 3 to 4 minutes. Finish the vegetables by adding the remaining 1 tablespoon of soy sauce.", "Ingredients": ["Pepper", "Peas", "Carrot", "Vegetables"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 3000000000}, "Cookware": ["Bowl", "Plate"], "Description": "Divide the rice into 4 portions and serve the cooked salmon and vegetables evenly.\n\nOptional Servings: Serve with a hard boiled egg or a fried egg.", "Ingredients": ["Rice", "Vegetables", "Salmon"], "Number": 4, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/spicy_salmon_vegetables.png", "Serves": 1, "CookTime": {"ticks": 21000000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/hot_pepper.png", "Name": "<PERSON><PERSON>", "Quantity": "10 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/garlic.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "2"}, {"UrlIcon": "ms-appx:///Assets/Icons/sauce.png", "Name": "Sauce", "Quantity": "20 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "7,5 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salmon.png", "Name": "Salmon", "Quantity": "170 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Pepper", "Quantity": "170 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/carrot.png", "Name": "Carrot", "Quantity": "4"}, {"UrlIcon": "ms-appx:///Assets/Icons/rice.png", "Name": "Rice", "Quantity": "800 gr"}], "Calories": "190 kcal", "Reviews": [{"Id": "44d12b82-e04f-469a-b183-96745f75b6b4", "RecipeId": "f907eefe-27cb-6bca-08a2-af356012d237", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Spicy Salmon and Vegetables is a protein meal with low calories", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 2, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Lunch", "Color": "#507FF7"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Tomato and garlic pasta", "UserId": "7f7cddd2-2e1e-4a99-a6e3-88575d2f66f1", "Id": "f947eefe-27cb-6bca-18a2-af356012d237", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": 3000000000}, "Cookware": ["<PERSON><PERSON><PERSON>", "Tablespoon"], "Description": "Preheat the broiler to low heat.\n\nWhisk together the chili garlic sauce, 2 tablespoons of soy sauce, 1 tablespoon of sesame oil, sugar and vinegar until combined.", "Ingredients": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sauce", "Soy", "Oil", "Sugar", "<PERSON><PERSON><PERSON>"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": **********}, "Cookware": ["Bowl", "Cookie sheet", "Non-Stick Foil", "<PERSON><PERSON><PERSON>"], "Description": "Add the cut up salmon to the bowl and mix until coated.\n\nLine a cookie sheet with Non-Stick Foil and evenly spread out the salmon and cook under the broiler for 8 to 10 minutes or until browned and cooked throughout.", "Ingredients": ["Salmon"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "CookTime": {"ticks": **********}, "Cookware": ["Pan", "Tablespoon"], "Description": "Pour the remaining 1 tablespoon of sesame oil into a large frying pan on high heat and add in the bell peppers, snow peas and carrot and stir-fry for 3 to 4 minutes.\n\nFinish the vegetables by adding the remaining 1 tablespoon of soy sauce.", "Ingredients": ["Pepper", "Peas", "Carrot"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 3000000000}, "Cookware": ["Bowl", "Plate"], "Description": "Divide the rice into 4 portions and serve the cooked salmon and vegetables evenly.\n\nOptional Servings: Serve with a hard boiled egg or a fried egg.", "Ingredients": ["Rice", "Salmon"], "Number": 4, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/tomato_garlic_pasta.png", "Serves": 1, "CookTime": {"ticks": 21000000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/hot_pepper.png", "Name": "<PERSON><PERSON>", "Quantity": "10 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/garlic.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "2"}, {"UrlIcon": "ms-appx:///Assets/Icons/sauce.png", "Name": "Sauce", "Quantity": "20 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "7,5 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salmon.png", "Name": "Salmon", "Quantity": "170 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Pepper", "Quantity": "170 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/carrot.png", "Name": "Carrot", "Quantity": "4"}, {"UrlIcon": "ms-appx:///Assets/Icons/rice.png", "Name": "Rice", "Quantity": "800 gr"}], "Calories": "190 kcal", "Reviews": [{"Id": "43717c2a-bb04-48d8-ba2a-c16070f26299", "RecipeId": "f947eefe-27cb-6bca-18a2-af356012d237", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Tomato and garlic pasta good mix between carbs and vegetables", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 2, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Lunch", "Color": "#507FF7"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Homemade lasagna", "UserId": "cf12f68f-ccc4-4740-87b3-acc984ac6821", "Id": "be9c0be1-3980-4331-b6df-0975de70251a", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": 18000000000}, "Cookware": ["Pot", "Tablespoon"], "Description": "Put a large pot of salted water (1 tablespoon of salt for every 2 quarts of water) on the stovetop on high heat.\n\nIt can take a while for a large pot of water to come to a boil (this will be your pasta water), so prepare the sauce in the next steps while the water is heating.", "Ingredients": ["Water", "Salt", "Sauce"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": **********0}, "Cookware": ["<PERSON><PERSON><PERSON>", "Tablespoon", "<PERSON>poon", "Bowl"], "Description": "In a large skillet heat 2 teaspoons of olive oil on medium-high heat.\n\nAdd the ground beef and cook until it is lightly browned on all sides.\n\nRemove the beef with a slotted spoon to a bowl.\n\nDrain off all but a tablespoon of fat.", "Ingredients": ["Olive oil", "Ground beef"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "CookTime": {"ticks": **********}, "Cookware": ["Pan", "Tablespoon"], "Description": "Add the diced bell pepper and onions to the skillet (in the photo we are using yellow bell pepper and red onions).\n\nCook for 4 to 5 minutes, until the onions are translucent and the peppers softened.\n\nAdd the minced garlic and cook half a minute more.\n\nReturn the browned ground beef to the pan.\n\nStir to combine, reduce the heat to low, and cook for another 5 minutes.", "Ingredients": ["Pepper", "Onions", "<PERSON><PERSON><PERSON>", "Ground beef"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 27000000000}, "Cookware": ["Pan", "Aluminum foil"], "Description": "Cover the lasagna pan with aluminum foil, tented slightly so it doesn't touch the noodles or sauce.\n\nBake at 375°F for 45 minutes. Uncover in the last 10 minutes if you'd like more of a crusty top or edges.", "Ingredients": ["Sauce"], "Number": 4, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/homemade_lasagna.png", "Serves": 1, "CookTime": {"ticks": 63000000000}, "Difficulty": 1, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/olive.png", "Name": "Olive oil", "Quantity": "20 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "100 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt and pepper", "Quantity": "2 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sauce.png", "Name": "Sauce", "Quantity": "828 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/meat.png", "Name": "Ground beef", "Quantity": "454 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/onion.png", "Name": "Onions", "Quantity": "26 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/garlic.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "2"}], "Calories": "425 kcal", "Reviews": [{"Id": "537802b0-aa01-4e1d-8dd6-c6c60c8614e6", "RecipeId": "be9c0be1-3980-4331-b6df-0975de70251a", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Homemade lasagna very taste to eat from home", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 2, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Lunch", "Color": "#507FF7"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Pork Stew", "UserId": "ebf3be76-2ff2-4325-94ec-ab9356daa8d2", "Id": "a0e8131c-4853-4d3f-a2d7-b6f54dc38a41", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": **********0}, "Cookware": ["Pot", "Tablespoon"], "Description": "In a large pot or dutch oven, brown pork with onions in 1 tablespoon olive oil (it doesn't have to be cooked through).\n\nRemove from pot and set aside.", "Ingredients": ["Pork", "Onion", "Olive oil"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": 3**********}, "Cookware": ["Pot"], "Description": "Add broth, tomatoes, and spices making sure to scrape up any brown bits from the bottom of the pot.\n\nAdd the pork back into the pot and bring to a boil, reduce heat and simmer covered for 1 hour (or until pork is fairly tender).", "Ingredients": ["Tomato", "Spice", "Pork"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": **********0}, "Cookware": ["Pot"], "Description": "Add potatoes, sweet potatoes, carrots, and mushrooms.\n\nBring to a boil, reduce heat and simmer covered for 30 minutes or until vegetables are tender.\n\nRemove lid and stir in green beans.\n\nThicken if desired (below) and simmer an additional 10 minutes uncovered.", "Ingredients": ["Potato", "Carrot", "Mushroom", "Green beans"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/pork_stew.png", "Serves": 1, "CookTime": {"ticks": 72000000000}, "Difficulty": 1, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/olive.png", "Name": "Olive oil", "Quantity": "20 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/pork.png", "Name": "Pork", "Quantity": "680 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/tomato.png", "Name": "Tomato", "Quantity": "425 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/potatoe.png", "Name": "Potato", "Quantity": "156 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/carrot.png", "Name": "Carrot", "Quantity": "2"}, {"UrlIcon": "ms-appx:///Assets/Icons/onion.png", "Name": "Onions", "Quantity": "26 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/mushroom.png", "Name": "Mushrooms", "Quantity": "96 gr"}], "Calories": "550 kcal", "Reviews": [{"Id": "5d5f45db-dd02-449c-acd9-10b3dc51e011", "RecipeId": "a0e8131c-4853-4d3f-a2d7-b6f54dc38a41", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Pork Stew has good nutritional quality for work out", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 2, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Lunch", "Color": "#507FF7"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Grill Chicken", "UserId": "697b0ccf-ddaf-4c67-bc69-377bc377c2e0", "Id": "8a14332d-9791-41f8-8f31-8e46c8c5020c", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": **********}, "Cookware": ["Bowl"], "Description": "In a large bowl, whisk the salt in the water to dissolve.\n\nAdd the chicken breasts to the brine.\n\nPut in the refrigerator and chill for 30 minutes.", "Ingredients": ["Salt", "Water", "Chicken"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": **********}, "Cookware": ["Grill", "Pan"], "Description": "Arrange your grill so that one side is for high direct heat, and the other side is cooler.\n\nAlternatively, you can use a grill pan, set over medium-high heat.\n\nRemove chicken breasts from brine and pat dry.\n\nCoat with olive oil, and sprinkle evenly with paprika.", "Ingredients": ["Chicken", "Olive oil", "<PERSON><PERSON><PERSON>"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": **********0}, "Cookware": ["Grill", "Pan"], "Description": "Brush some olive oil on the grill grates.\n\nPlace chicken breasts on the hot side of the grill (or on the grill pan).\n\nLet the chicken grill, undisturbed, until the pieces start getting some grill marks (you can lift up one to check).\n\nWhen the chicken pieces have browned on one side, turn them over, and move them to the cooler side of the grill (low heat, not no heat).\n\nCover, and let them finish cooking.", "Ingredients": ["Olive oil", "Chicken"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/grill_chicken.png", "Serves": 1, "CookTime": {"ticks": **********0}, "Difficulty": 1, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/olive.png", "Name": "Olive oil", "Quantity": "20 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/chicken.png", "Name": "Chicken", "Quantity": "900 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "946 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt", "Quantity": "33 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/hot_pepper.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "15 gr"}], "Calories": "525 kcal", "Reviews": [{"Id": "38bf80d7-c477-4d6b-ab38-cb172e98267b", "RecipeId": "8a14332d-9791-41f8-8f31-8e46c8c5020c", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Grill Chicken tasty meal to get out of the simple chicken recipe.", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 2, "UrlIcon": "ms-appx:///Assets/Icons/pancakes.png", "Name": "Lunch", "Color": "#507FF7"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Tomato pasta", "UserId": "0fb99c42-28a6-4e15-be40-6d2254f18f3f", "Id": "dcbbc95a-2b23-4311-b58f-ded654f93bf4", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": 3000000000}, "Cookware": ["Pot"], "Description": "Place tomatoes in a large pot and cover with cold water.\n\nBring just to a boil.\n\nPour off water, and cover again with cold water.\n\nPeel the skin off tomatoes and cut into small pieces.", "Ingredients": ["Tomato", "Water"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": 3000000000}, "Cookware": ["Pot"], "Description": "Bring a large pot of lightly salted water to a boil.\n\nCook angel hair pasta in the boiling water, stirring occasionally, until tender yet firm to the bite, 4 to 5 minutes.", "Ingredients": ["Salt", "Water"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "CookTime": {"ticks": 15000000000}, "Cookware": ["<PERSON><PERSON><PERSON>"], "Description": "Meanwhile, heat olive oil in a large skillet or pan, making sure there is enough to cover the bottom of the pan, and sauté garlic until opaque but not browned. Stir in tomato paste.\n\nImmediately stir in the tomatoes, salt, and pepper.\n\nReduce heat, and simmer until pasta is ready, adding basil at the end.", "Ingredients": ["Olive oil", "<PERSON><PERSON><PERSON>", "Tomato", "Salt", "Pepper", "<PERSON>"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": **********}, "Cookware": ["Plates", "Pot"], "Description": "Drain pasta, do not rinse in cold water. Toss with a bit of olive oil, then mix into the sauce.\n\nReduce heat as low as possible. Keep warm, uncovered, for about 10 minutes when it is ready to serve. Garnish generously with fresh Parmesan cheese.", "Ingredients": ["Water", "Olive oil"], "Number": 4, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/tomato_pasta.png", "Serves": 1, "CookTime": {"ticks": 27000000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/olive.png", "Name": "Olive oil", "Quantity": "10 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/tomato.png", "Name": "Tomato", "Quantity": "907 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "578 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt and pepper", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/garlic.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "15 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/basil.png", "Name": "<PERSON>", "Quantity": "10 gr"}], "Calories": "380 kcal", "Reviews": [{"Id": "f89a0050-8ccc-4026-b452-227e35c6e03b", "RecipeId": "dcbbc95a-2b23-4311-b58f-ded654f93bf4", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Tomato pasta is a simple recipe where the sauce has a delicious taste", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 3, "UrlIcon": "ms-appx:///Assets/Icons/fork_and_knife_with_plate.png", "Name": "Dinner", "Color": "#F16583"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Chicken Ramen Bowl", "UserId": "bed8ff9c-08f7-4c44-b5a3-4d95708f245e", "Id": "e4c6ea0e-1907-41a1-b463-4f114da0535a", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": **********}, "Cookware": ["Pan"], "Description": "In a large saucepan over medium heat, heat the oil.\n\nAdd the scallions, garlic, carrot, mushrooms, and cabbage. Cook, stirring often, for 3 minutes, or until the vegetables soften.\n\nStir in the ginger and brown sugar (if using) and cook for 30 seconds.\n\nFinally add the chicken broth and bring to a simmer.\n\nSimmer for 10 minutes.", "Ingredients": ["Oil", "Mushrooms", "Scallion", "<PERSON><PERSON><PERSON>", "Carrot", "Cabbage", "<PERSON>", "Sugar", "Chicken"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": 3000000000}, "Cookware": ["Pot", "<PERSON>poon"], "Description": "While the broth is simmering, bring a pot of water to a boil. Set a bowl of cold ice water nearby.\n\nUse a slotted spoon to gently lower the eggs, still in the shell, into the water. Simmer for 7 minutes for eggs that are soft and a little runny in the center, or 9 minutes for hard-boiled eggs.\n\nTransfer the eggs to the bowl of cold water and set aside until ready to serve.", "Ingredients": ["Water", "Egg"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "CookTime": {"ticks": 1800000000}, "Cookware": ["Pot", "<PERSON><PERSON>"], "Description": "Return the pot of water to a boil.\n\nAdd the noodles and cook for 3 minutes, or according to the package directions, until tender.\n\nDrain in a colander.", "Ingredients": ["Water", "Nodles"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 4200000000}, "Cookware": ["Bowls", "<PERSON>poon"], "Description": "Divide the noodles among 4 large bowls. Ladle the broth and vegetables over the noodles.\n\nRemove the eggs from the ice water. Tap the shells with a spoon to crack and then peel off the shells.\n\nPat the eggs dry and cut them in half.", "Ingredients": ["Noodles", "Egg"], "Number": 4, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/chicken_ramen_bowl.png", "Serves": 1, "CookTime": {"ticks": 18000000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/olive.png", "Name": "Oil", "Quantity": "20 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/mushroom.png", "Name": "Mushrooms", "Quantity": "113 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/onion.png", "Name": "Scallion", "Quantity": "57 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/garlic.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "3 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/carrot.png", "Name": "Carrot", "Quantity": "54 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "900 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/spaghetti.png", "Name": "Noodles", "Quantity": "625 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/chicken.png", "Name": "Chicken", "Quantity": "1360 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/egg.png", "Name": "Egg", "Quantity": "200 gr"}], "Calories": "290 kcal", "Reviews": [{"Id": "2a21590b-1cc5-4b34-a596-6ab68f1013ca", "RecipeId": "e4c6ea0e-1907-41a1-b463-4f114da0535a", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Chicken Ramen Bowl is a delicious pasta with high protein qualifications", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 3, "UrlIcon": "ms-appx:///Assets/Icons/fork_and_knife_with_plate.png", "Name": "Dinner", "Color": "#F16583"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Pasta Salad Bowl", "UserId": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Id": "c7dd4999-0084-45b3-a2c1-c5b0750b2bd0", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": 3000000000}, "Cookware": ["Pot"], "Description": "Gather all ingredients.\n\nBring a large pot of lightly salted water to a boil.", "Ingredients": ["Salt", "Water"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": 7200000000}, "Cookware": ["Pot"], "Description": "Cook pasta in the boiling water, stirring occasionally, until tender yet firm to the bite, about 10 to 12 minutes; rinse under cold water and drain.", "Ingredients": ["Pasta"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 7800000000}, "Cookware": ["Bowl"], "Description": "Whisk Italian dressing and salad spice mix together until smooth.\n\nCombine pasta, tomatoes, bell peppers, and olives in a salad bowl; pour dressing over salad and toss to coat.", "Ingredients": ["Italian dressing", "Pasta", "Tomato", "Pepper", "<PERSON>"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/pasta_salad_bowl.png", "Serves": 1, "CookTime": {"ticks": 18000000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/olive.png", "Name": "<PERSON>", "Quantity": "64 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt and pepper", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "800 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/spaghetti.png", "Name": "Pasta", "Quantity": "454 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sauce.png", "Name": "Italian dressing", "Quantity": "454 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/tomato.png", "Name": "Tomato", "Quantity": "398 gr"}], "Calories": "360 kcal", "Reviews": [{"Id": "3a4dc143-a027-4e33-b3c2-ce4a669d16d4", "RecipeId": "c7dd4999-0084-45b3-a2c1-c5b0750b2bd0", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Pasta Salad Bowl mix vegetable nutrients with carbohydrates.", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 3, "UrlIcon": "ms-appx:///Assets/Icons/fork_and_knife_with_plate.png", "Name": "Dinner", "Color": "#F16583"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Roast Beef Tenderloin", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Id": "d54a7cfe-e509-4a14-bc75-0f24679987a7", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": **********}, "Cookware": ["Sheet Pan", "Teaspoon"], "Description": "Heat the oven to 450 degrees. Line a large sheet pan with foil. Place the beef on the pan.\n\nMix the butter, soy sauce, Worcestershire sauce and sugar in a small bowl until the sugar dissolves.\n\nPour the mixture all over the beef, using your hands to spread it around.\n\nSprinkle the beef generously with salt (about 2 teaspoons) and press in an even coating of pepper (about 1 teaspoon).", "Ingredients": ["<PERSON><PERSON>", "Butter", "Soy sauce", "Worcestershire sauce", "Sugar", "Pepper"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": 18000000000}, "Cookware": ["Meat thermometer", "Cutting board"], "Description": "Roast until browned and a meat thermometer inserted in the center registers 120 to 125 degrees for medium-rare, 25 to 30 minutes. (Start checking at 20 minutes to make sure you don’t overcook the meat.)\n\nUse the foil to lift and transfer the beef with its juices to a cutting board.\n\nLet rest for 15 to 20 minutes.\n\nThe internal temperature of the meat will rise 5 to 10 degrees as it rests.", "Ingredients": ["<PERSON><PERSON>"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 3000000000}, "Cookware": ["Cutting board", "Plate"], "Description": "Transfer the beef to the cutting board, reserving the foil with its juices, and cut the beef into slices for serving.\n\nArrange on a serving platter and pour over all of the juices from the foil and cutting board. Sprinkle with parsley, if you’d like, and serve with the horseradish sauce.", "Ingredients": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Horseradish sauce"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/roast_beef_tenderloin.png", "Serves": 1, "CookTime": {"ticks": 30000000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/meat.png", "Name": "<PERSON><PERSON>", "Quantity": "1814 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/butter.png", "Name": "Butter", "Quantity": "20 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Pepper", "Quantity": "50 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/parsley.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "113 gr"}], "Calories": "375 kcal", "Reviews": [{"Id": "4a07d3ea-2211-49bc-83ab-20e05dd48005", "RecipeId": "d54a7cfe-e509-4a14-bc75-0f24679987a7", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Roast Beef Tenderloin has a high caloric percentage and you can taste several sauces", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 3, "UrlIcon": "ms-appx:///Assets/Icons/fork_and_knife_with_plate.png", "Name": "Dinner", "Color": "#F16583"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Italian Spaghetti with meatballs", "UserId": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "Id": "012f6256-b771-45e1-9305-975923664448", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": **********0}, "Cookware": ["Bowl", "Teaspoon"], "Description": "In a large bowl, combine ground beef, bread crumbs, parsley, parmesan, 1/4 teaspoon black pepper, garlic powder and beaten egg.\n\nMix well and form into 12 balls.\n\nStore, covered, in refrigerator until needed.", "Ingredients": ["Ground beef", "Bread crumbs", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Egg"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": **********0}, "Cookware": ["Saucepan"], "Description": "In a large saucepan over medium heat, saute onion and garlic in olive oil until onion is translucent. Stir in tomatoes, salt, sugar and bay leaf.", "Ingredients": ["Onion", "<PERSON><PERSON><PERSON>", "Olive oil", "Tomato", "Salt", "Sugar", "Bay leaf"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 48000000000}, "Cookware": ["Teaspoon", "Plate"], "Description": "Cover, reduce heat to low, and simmer 50 minutes. Stir in tomato paste, basil, 1/2 teaspoon pepper and meatballs and simmer 30 minutes more.", "Ingredients": ["Tomato", "<PERSON>", "Pepper"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/italian_spaghetti_meatballs.png", "Serves": 1, "CookTime": {"ticks": 84000000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/meat.png", "Name": "Ground beef", "Quantity": "453 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Bread crumbs", "Quantity": "60 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/parsley.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/cheese.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/garlic.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "1.25 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/egg.png", "Name": "Egg", "Quantity": "1"}, {"UrlIcon": "ms-appx:///Assets/Icons/onion.png", "Name": "Onion", "Quantity": "13 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/olive.png", "Name": "Olive oil", "Quantity": "13 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/tomato.png", "Name": "Tomato", "Quantity": "964 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt and pepper", "Quantity": "20 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/leaf.png", "Name": "Bay leaf", "Quantity": "1"}, {"UrlIcon": "ms-appx:///Assets/Icons/basil.png", "Name": "<PERSON>", "Quantity": "7.5 gr"}], "Calories": "411 kcal", "Reviews": [{"Id": "c827686b-48d7-49c8-8508-403734598c5d", "RecipeId": "012f6256-b771-45e1-9305-975923664448", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Italian spaghetti and meatballs mix protein with carbs", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 3, "UrlIcon": "ms-appx:///Assets/Icons/fork_and_knife_with_plate.png", "Name": "Dinner", "Color": "#F16583"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Pork Dumplings", "UserId": "3f848dba-f972-493a-bf52-0cd93958f4b2", "Id": "e79f9e7c-4576-4e48-87d0-87f17561cdfc", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": 25800000000}, "Cookware": ["Bowl"], "Description": "Prepare dipping sauce: Combine soy sauce, rice vinegar, chives, sesame seeds, and chile sauce in a small bowl. Set aside.\n\n Mix pork, garlic, egg, chives, soy sauce, sesame oil, and ginger in a large bowl until thoroughly combined.", "Ingredients": ["Sauce", "Soy sauce", "Rice vinegar", "Sesame seeds", "Chile sauce", "Pork", "<PERSON><PERSON><PERSON>", "Egg", "Chives", "Sesame oil", "<PERSON>"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": 18000000000}, "Cookware": ["Tablespoon"], "Description": "Place a dumpling wrapper on a lightly floured work surface and spoon about 1 tablespoon of the filling in the middle.\n\nWet the edge with a little water and crimp together forming small pleats to seal the dumpling. Repeat to form remaining dumplings.", "Ingredients": ["Water"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 4200000000}, "Cookware": ["Tablespoon", "<PERSON><PERSON><PERSON>", "Pan", "Cup"], "Description": "Heat 1 to 2 tablespoons vegetable oil in a large skillet over medium-high heat. Place 8 to 10 dumplings in the pan and cook until browned, about 2 minutes per side. Pour in 1 cup of water; cover and cook until the dumplings are tender and pork is cooked through, about 5 minutes.", "Ingredients": ["Pork"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/pork_dumplings.png", "Serves": 1, "CookTime": {"ticks": 48000000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/sauce.png", "Name": "Sauce", "Quantity": "20 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/rice.png", "Name": "Rice vinager", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/hot_pepper.png", "Name": "Chile sauce", "Quantity": "10 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/pork.png", "Name": "Pork", "Quantity": "453 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/garlic.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/egg.png", "Name": "Egg", "Quantity": "1"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "834 ml"}], "Calories": "80 kcal", "Reviews": [{"Id": "486de7ef-015c-4fcc-abf9-4f844b696803", "RecipeId": "e79f9e7c-4576-4e48-87d0-87f17561cdfc", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Italian spaghetti and meatballs mix protein with carbs", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 3, "UrlIcon": "ms-appx:///Assets/Icons/fork_and_knife_with_plate.png", "Name": "Dinner", "Color": "#F16583"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "<PERSON><PERSON><PERSON>", "UserId": "7f7cddd2-2e1e-4a99-a6e3-88575d2f66f1", "Id": "a8dcfc92-a90b-49eb-8d7c-0a0cd1f595d0", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": **********}, "Cookware": ["Saucepan"], "Description": "Bring 2 cups blueberries, the sugar, and water to a simmer in a small saucepan over medium heat.", "Ingredients": ["Blueberries", "Sugar", "Water"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": **********}, "Cookware": ["Cup"], "Description": "Cook stirring frequently until berries have burst and the compote has thickened, about 5 – 8 minutes.\n\nThere should be 1 cup of the blueberry compote.", "Ingredients": ["Blueberries"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 3000000000}, "Cookware": ["Bowl"], "Description": "Pour mixture into a bowl and chill in refrigerator until cooled. Wait to assemble parfaits until you are almost ready to serve them so the granola stays crisp.", "Ingredients": ["Granola", "Yogurt"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/yogurt_parfait.png", "Serves": 1, "CookTime": {"ticks": 10200000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/blueberries.png", "Name": "Blueberries", "Quantity": "20 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "20 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/water.png", "Name": "Water", "Quantity": "100 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/flour.png", "Name": "Granola", "Quantity": "20 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/milk.png", "Name": "Yogurt", "Quantity": "82 gr"}], "Calories": "84 kcal", "Reviews": [{"Id": "934b3506-8996-4e56-99e6-6ab7cea36219", "RecipeId": "a8dcfc92-a90b-49eb-8d7c-0a0cd1f595d0", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Yogurt Parfait is a delicious and nutritious snack", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 4, "UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Snack", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "American Hotcakes", "UserId": "7f7cddd2-2e1e-4a99-a6e3-88575d2f66f1", "Id": "e1024abe-1bf5-40a7-a092-1d458f65b138", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": **********}, "Cookware": ["Tablespoon", "Bowl", "Electric hand beaters", "<PERSON><PERSON>"], "Description": "Mix 200g self-raising flour, 1 1/2 tablespoon baking powder, 1 tablespoon golden caster sugar and a pinch of salt together in a large bowl./n/nCreate a well in the centre with the back of your spoon then add 3 large eggs, 25g melted butter and 200ml milk.\n\nWhisk together either with a balloon whisk or electric hand beaters until smooth then pour into a jug.", "Ingredients": ["Flour", "Baking powder", "Sugar", "Salt", "Egg", "Butter", "Milk"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": **********}, "Cookware": ["Non-stick frying pan"], "Description": "Heat a small knob of butter and 1 tablespoon of oil in a large, non-stick frying pan over a medium heat.\n\nWhen the butter looks frothy, pour in rounds of the batter, approximately 8cm wide.\n\nMake sure you don’t put the pancakes too close together as they will spread during cooking.\n\nCook the pancakes on one side for about 1-2 mins or until lots of tiny bubbles start to appear and pop on the surface.\n\nFlip the pancakes over and cook for a further minute on the other side. Repeat until all the batter is used up.", "Ingredients": ["Butter"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 3000000000}, "Cookware": ["Plate"], "Description": "Serve your pancakes stacked up on a plate with a drizzle of maple syrup and any of your favourite toppings.", "Ingredients": ["Maple syrup"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/american_hotcakes.png", "Serves": 1, "CookTime": {"ticks": 18000000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/flour.png", "Name": "Flour", "Quantity": "200 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt", "Quantity": "5 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/egg.png", "Name": "Egg", "Quantity": "3"}, {"UrlIcon": "ms-appx:///Assets/Icons/butter.png", "Name": "Butter", "Quantity": "25 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/honey.png", "Name": "Maple syrup", "Quantity": "80 ml"}], "Calories": "268 kcal", "Reviews": [{"Id": "2b71f06e-f561-4321-a35f-fa4abc067fe5", "RecipeId": "e1024abe-1bf5-40a7-a092-1d458f65b138", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "This is a traditional meal in italia", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 4, "UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Snack", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "<PERSON><PERSON><PERSON>", "UserId": "cf12f68f-ccc4-4740-87b3-acc984ac6821", "Id": "052e70cb-f800-46aa-bc10-f21d10dadb14", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": **********}, "Cookware": ["Plate"], "Description": "Get bread, hummus and avocado to get ready the ingredients. Then start to spread one slice of bread with hummus and the other with avocado.", "Ingredients": ["Avocado", "Bread", "<PERSON>mus"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": **********}, "Cookware": ["Plate"], "Description": "For this step, it's important to get several vegetables that provide the recipe nutrients.\n\nFill the sandwich with greens, bell pepper, cucumber and carrot.", "Ingredients": ["Greens", "Pepper", "<PERSON><PERSON><PERSON>ber", "Carrot"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 3000000000}, "Cookware": ["Plate"], "Description": "For getting the nutrients, it's very important to follow the last two steps very strict.\n\n Slice in half and serve.", "Ingredients": ["Bread"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/veggie_sandwich.png", "Serves": 1, "CookTime": {"ticks": **********}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/avocado.png", "Name": "Avocado", "Quantity": "25 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Bread", "Quantity": "30 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/leafy_green.png", "Name": "Greens", "Quantity": "32.5 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Pepper", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/cucumber.png", "Name": "<PERSON><PERSON><PERSON>ber", "Quantity": "75 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/carrot.png", "Name": "Carrot", "Quantity": "64 gr"}], "Calories": "265 kcal", "Reviews": [{"Id": "0b6004c6-ea94-4716-b9c9-889c3570ed84", "RecipeId": "052e70cb-f800-46aa-bc10-f21d10dadb14", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "This is a traditional meal in italia", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 4, "UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Snack", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Prawns Provencale", "UserId": "ebf3be76-2ff2-4325-94ec-ab9356daa8d2", "Id": "6e3e90fb-f2fe-4a98-81ff-85033a8a4010", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": 7200000000}, "Cookware": ["Sheep pan", "Aluminum foil", "Tablespoon", "<PERSON>mp"], "Description": "Preheat oven to 475 degrees F (245 degrees C). Line a sheet pan with aluminum foil. Brush with 1 tablespoon olive oil.\n\nCarefully remove shells and legs of prawns; leave the tail attached. To butterfly the prawns, cut a slit with a small sharp knife lengthwise down the belly side of shrimp, almost to the skin on the back. Open out like a book.", "Ingredients": ["<PERSON>mp", "Prawns", "Olive oil"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": 1800000000}, "Cookware": ["Mortar", "Tablespoon", "Cup"], "Description": "Place chopped garlic, large pinch kosher salt, oregano, and thyme in a mortar; pound and stir with a pestle a few seconds. Add 1 tablespoon fresh parsley.\n\nPound and stir mixture until it turns into a paste, 1 or 2 minutes. Add 1/3 cup olive oil; mix about 1 minute to infuse olive oil with herbs and garlic.", "Ingredients": ["<PERSON><PERSON><PERSON>", "Oregano", "Thyme", "<PERSON><PERSON><PERSON>", "Olive oil"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "CookTime": {"ticks": 1800000000}, "Cookware": ["Bowl", "Fork"], "Description": "Place bread crumbs in a mixing bowl. Transfer herb-garlic mixture to breadcrumbs. Add a pinch of salt, black pepper, pinch cayenne, remaining chopped parsley, and grated cheese. Mix with a fork to distribute ingredients evenly. Pinch a bit of the mixture; if it feels a bit dry and doesn't stick to your finger, drizzle in a bit more olive oil. Stir until mixture reaches desired consistency. 2 to 3 minutes.", "Ingredients": ["Bread crumbs", "<PERSON><PERSON><PERSON>", "Grated cheese", "<PERSON><PERSON><PERSON>", "Cayenne"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": **********}, "Cookware": ["Baking sheet"], "Description": "Lightly but thoroughly coat cut side of prawns with crumb mixture; place on prepared baking sheet.\n\nBake in preheated oven until cooked through and tails curl up, 8 to 10 minutes.", "Ingredients": ["Prawns", "Bread crumb"], "Number": 4, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/prawns_provencale.png", "Serves": 1, "CookTime": {"ticks": 16800000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/olive.png", "Name": "Olive oil", "Quantity": "20 ml"}, {"UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Bread crumbs", "Quantity": "150 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/prawns.png", "Name": "Prawns", "Quantity": "30 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt and pepper", "Quantity": "10 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/parsley.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "8.5 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/cheese.png", "Name": "Grated cheese", "Quantity": "42.5 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/hot_pepper.png", "Name": "Cayenne", "Quantity": "5.3 gr"}], "Calories": "385 kcal", "Reviews": [{"Id": "f84b52c1-3536-41ca-a077-3d423de32ff8", "RecipeId": "6e3e90fb-f2fe-4a98-81ff-85033a8a4010", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Provide seafood benefits and calories", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 4, "UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Snack", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "Salmon Croquettes", "UserId": "697b0ccf-ddaf-4c67-bc69-377bc377c2e0", "Id": "256cdf86-04b3-444f-b496-a364d280d66f", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": 3000000000}, "Cookware": ["Bowl"], "Description": "Add panko and flour to a bowl and mix.\nAdd bell peppers, canned salmon, garlic, salt, pepper, egg, mayonnaise, Worcestershire sauce and cilantro. Mix until incorporated.", "Ingredients": ["<PERSON><PERSON>", "Flour", "Pepper", "Canned salmon", "<PERSON><PERSON><PERSON>", "Salt"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": 4200000000}, "Cookware": ["<PERSON><PERSON><PERSON>"], "Description": "Shape into 6-8 patties and heat oil in large skillet over medium high heat.", "Ingredients": ["Oil"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 1800000000}, "Cookware": ["<PERSON><PERSON><PERSON>"], "Description": "Add patties to the skillet and cook for 2-3 minutes on each side or until golden brown.", "Ingredients": ["Pepper", "Egg", "Mayonnaise", "Worcestershire sauce", "Cilantro"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/salmon_croquettes.png", "Serves": 1, "CookTime": {"ticks": **********}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/panko.png", "Name": "<PERSON><PERSON>", "Quantity": "50 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/flour.png", "Name": "Flour", "Quantity": "34 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt and pepper", "Quantity": "2.5 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salmon.png", "Name": "Canned salmon", "Quantity": "141 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/garlic.png", "Name": "<PERSON><PERSON><PERSON>", "Quantity": "5 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/egg.png", "Name": "Egg", "Quantity": "1"}, {"UrlIcon": "ms-appx:///Assets/Icons/mayonnaise.png", "Name": "Mayonnaise", "Quantity": "2.5 gr"}], "Calories": "150 kcal", "Reviews": [{"Id": "52728a86-a404-42be-a664-4c2e02aa250b", "RecipeId": "256cdf86-04b3-444f-b496-a364d280d66f", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Provide seafood benefits and calories", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 4, "UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Snack", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}, {"Name": "<PERSON><PERSON>", "UserId": "0fb99c42-28a6-4e15-be40-6d2254f18f3f", "Id": "79433407-c5d0-4a34-831b-dec51e48bb7d", "Steps": [{"Name": "Getting start", "CookTime": {"ticks": **********}, "Cookware": ["Muffin tin", "Bowl"], "Description": "Preheat the oven to 350 degrees F (175 degrees C). Grease a 12-cup muffin tin or line cups with paper liners. Sift flour, baking powder, baking soda, and salt together in a bowl; set aside.", "Ingredients": ["Flour", "Baking powder", "Baking soda", "Salt"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "First step", "CookTime": {"ticks": 15000000000}, "Cookware": ["Bowl", "Muffin cups"], "Description": "Mix bananas, sugar, egg, and melted butter in a separate large bowl until well combined; fold in flour mixture until smooth.\n\nSpoon batter into the prepared muffin cups, filling each 2/3 full.", "Ingredients": ["Banana<PERSON>", "Sugar", "Egg", "Butter"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "CookTime": {"ticks": 21000000000}, "Cookware": ["Muffin tin"], "Description": "Bake in the preheated oven until tops spring back when lightly pressed, about 25 to 30 minutes.\n\nCool briefly in the tin, then transfer to a wire rack to cool completely.", "Ingredients": ["Butter"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "ImageUrl": "ms-appx:///Assets/Recipes/banana_muffins.png", "Serves": 1, "CookTime": {"ticks": 42000000000}, "Difficulty": 0, "Ingredients": [{"UrlIcon": "ms-appx:///Assets/Icons/flour.png", "Name": "Flour", "Quantity": "192 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/salt.png", "Name": "Salt and pepper", "Quantity": "2 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/banana.png", "Name": "Banana<PERSON>", "Quantity": "600 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/sugar.png", "Name": "Sugar", "Quantity": "150 gr"}, {"UrlIcon": "ms-appx:///Assets/Icons/egg.png", "Name": "Egg", "Quantity": "1"}, {"UrlIcon": "ms-appx:///Assets/Icons/butter.png", "Name": "Butter", "Quantity": "73 gr"}], "Calories": "348 kcal", "Reviews": [{"Id": "bf68b45c-038a-4a75-8ff7-a5771d4c6a65", "RecipeId": "79433407-c5d0-4a34-831b-dec51e48bb7d", "PublisherName": "Roberta any", "UrlAuthorImage": "ms-appx:///Assets/Profiles/roberta_any.png", "CreatedBy": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Date": "2022-07-12T00:00:00Z", "Description": "Awesome recipe", "Likes": ["bb708644-d5cd-45ad-b565-07a0c4d0b320", "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe"], "Dislikes": ["02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "3f848dba-f972-493a-bf52-0cd93958f4b2"]}], "Details": "Delicious natural dessert made with fruit", "Creator": {"Id": "3c896419-e280-40e7-8552-240635566fed", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "FullName": "<PERSON>", "Description": "Passionate about food and life", "Email": "<EMAIL>", "PhoneNumber": "", "Password": "123", "Followers": 450, "Following": 124, "Recipes": 0}, "Category": {"Id": 4, "UrlIcon": "ms-appx:///Assets/Icons/baguette_bread.png", "Name": "Snack", "Color": "#7A67F8"}, "Date": "2022-10-15T00:00:00Z", "Save": false}]