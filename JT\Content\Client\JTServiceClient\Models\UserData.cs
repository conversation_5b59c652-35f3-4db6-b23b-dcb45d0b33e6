// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace JT.Client.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class UserData : IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The description property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Description { get; set; }
#nullable restore
#else
        public string Description { get; set; }
#endif
        /// <summary>The email property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Email { get; set; }
#nullable restore
#else
        public string Email { get; set; }
#endif
        /// <summary>The followers property</summary>
        public long? Followers { get; set; }
        /// <summary>The following property</summary>
        public long? Following { get; set; }
        /// <summary>The fullName property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? FullName { get; set; }
#nullable restore
#else
        public string FullName { get; set; }
#endif
        /// <summary>The id property</summary>
        public Guid? Id { get; set; }
        /// <summary>The isCurrent property</summary>
        public bool? IsCurrent { get; set; }
        /// <summary>The password property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Password { get; set; }
#nullable restore
#else
        public string Password { get; set; }
#endif
        /// <summary>The phoneNumber property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? PhoneNumber { get; set; }
#nullable restore
#else
        public string PhoneNumber { get; set; }
#endif
        /// <summary>The recipes property</summary>
        public long? Recipes { get; set; }
        /// <summary>The urlProfileImage property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? UrlProfileImage { get; set; }
#nullable restore
#else
        public string UrlProfileImage { get; set; }
#endif
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::JT.Client.Models.UserData"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::JT.Client.Models.UserData CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::JT.Client.Models.UserData();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "description", n => { Description = n.GetStringValue(); } },
                { "email", n => { Email = n.GetStringValue(); } },
                { "followers", n => { Followers = n.GetLongValue(); } },
                { "following", n => { Following = n.GetLongValue(); } },
                { "fullName", n => { FullName = n.GetStringValue(); } },
                { "id", n => { Id = n.GetGuidValue(); } },
                { "isCurrent", n => { IsCurrent = n.GetBoolValue(); } },
                { "password", n => { Password = n.GetStringValue(); } },
                { "phoneNumber", n => { PhoneNumber = n.GetStringValue(); } },
                { "recipes", n => { Recipes = n.GetLongValue(); } },
                { "urlProfileImage", n => { UrlProfileImage = n.GetStringValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("description", Description);
            writer.WriteStringValue("email", Email);
            writer.WriteLongValue("followers", Followers);
            writer.WriteLongValue("following", Following);
            writer.WriteStringValue("fullName", FullName);
            writer.WriteGuidValue("id", Id);
            writer.WriteBoolValue("isCurrent", IsCurrent);
            writer.WriteStringValue("password", Password);
            writer.WriteStringValue("phoneNumber", PhoneNumber);
            writer.WriteLongValue("recipes", Recipes);
            writer.WriteStringValue("urlProfileImage", UrlProfileImage);
        }
    }
}
#pragma warning restore CS0618
