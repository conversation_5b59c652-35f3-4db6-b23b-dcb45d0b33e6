using JT.Server.Entities;
using Microsoft.AspNetCore.Mvc;

namespace JT.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SkillController : ChefsControllerBase
{
	private readonly ILogger<SkillController> _logger;

	public SkillController(ILogger<SkillController> logger)
	{
		_logger = logger;
	}

	[HttpGet]
	public async Task<ActionResult<IEnumerable<SkillData>>> GetSkills()
	{
		try
		{
			var skills = await GetMockData<SkillData>("Skills.json");
			return Ok(skills);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting skills");
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("{id}")]
	public async Task<ActionResult<SkillData>> GetSkill(int id)
	{
		try
		{
			var skills = await GetMockData<SkillData>("Skills.json");
			var skill = skills.FirstOrDefault(s => s.Id == id);
			
			if (skill == null)
			{
				return NotFound();
			}

			return Ok(skill);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting skill {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("category/{category}")]
	public async Task<ActionResult<IEnumerable<SkillData>>> GetSkillsByCategory(string category)
	{
		try
		{
			var skills = await GetMockData<SkillData>("Skills.json");
			var filteredSkills = skills.Where(s => 
				string.Equals(s.Category, category, StringComparison.OrdinalIgnoreCase)).ToList();
			return Ok(filteredSkills);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting skills by category {Category}", category);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("industry/{industry}")]
	public async Task<ActionResult<IEnumerable<SkillData>>> GetSkillsByIndustry(string industry)
	{
		try
		{
			var skills = await GetMockData<SkillData>("Skills.json");
			var filteredSkills = skills.Where(s => 
				string.Equals(s.Industry, industry, StringComparison.OrdinalIgnoreCase)).ToList();
			return Ok(filteredSkills);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting skills by industry {Industry}", industry);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("trending")]
	public async Task<ActionResult<IEnumerable<SkillData>>> GetTrendingSkills()
	{
		try
		{
			var skills = await GetMockData<SkillData>("Skills.json");
			var trendingSkills = skills
				.Where(s => s.PopularityScore >= 80)
				.OrderByDescending(s => s.PopularityScore)
				.Take(10)
				.ToList();
			return Ok(trendingSkills);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting trending skills");
			return StatusCode(500, "Internal server error");
		}
	}
}
