using TransferRequestData = JT.Client.Models.TransferRequestData;

namespace JT.Business.Models;

public partial record TransferRequest : IChefEntity
{
	internal TransferRequest(TransferRequestData transferRequestData)
	{
		Id = transferRequestData.Id ?? Guid.Empty;
		UserId = transferRequestData.UserId ?? Guid.Empty;
		RequestTitle = transferRequestData.RequestTitle;
		CurrentLocation = new Location(transferRequestData.CurrentLocation);
		DestinationLocation = new Location(transferRequestData.DestinationLocation);
		CurrentSalaryGrade = transferRequestData.CurrentSalaryGrade;
		DesiredSalaryGrade = transferRequestData.DesiredSalaryGrade;
		CurrentFinancialGrade = transferRequestData.CurrentFinancialGrade;
		DesiredFinancialGrade = transferRequestData.DesiredFinancialGrade;
		Industry = transferRequestData.Industry;
		JobCategory = transferRequestData.JobCategory;
		TransferReason = transferRequestData.TransferReason;
		Status = transferRequestData.Status;
		Priority = transferRequestData.Priority;
		SubmissionDate = transferRequestData.SubmissionDate ?? DateTime.MinValue;
		ExpirationDate = transferRequestData.ExpirationDate ?? DateTime.MinValue;
		ViewCount = transferRequestData.ViewCount ?? 0;
		InterestedEmployers = transferRequestData.InterestedEmployers ?? 0;
		Documents = transferRequestData.Documents?.ToImmutableList() ?? ImmutableList<string>.Empty;
		RequiredSkills = transferRequestData.RequiredSkills?.ToImmutableList() ?? ImmutableList<string>.Empty;
		PreferredCompanySize = transferRequestData.PreferredCompanySize;
		RemoteWorkPreference = transferRequestData.RemoteWorkPreference;
		LanguageRequirements = transferRequestData.LanguageRequirements?.ToImmutableList() ?? ImmutableList<string>.Empty;
		CreatedBy = new UserProfile(transferRequestData.CreatedBy);
		Responses = transferRequestData.Responses?.Select(r => new EmployerResponse(r)).ToImmutableList() ?? ImmutableList<EmployerResponse>.Empty;
	}

	public Guid Id { get; init; }
	public Guid UserId { get; init; }
	public string? RequestTitle { get; init; }
	public Location CurrentLocation { get; init; }
	public Location DestinationLocation { get; init; }
	public string? CurrentSalaryGrade { get; init; }
	public string? DesiredSalaryGrade { get; init; }
	public string? CurrentFinancialGrade { get; init; }
	public string? DesiredFinancialGrade { get; init; }
	public string? Industry { get; init; }
	public string? JobCategory { get; init; }
	public string? TransferReason { get; init; }
	public string? Status { get; init; }
	public string? Priority { get; init; }
	public DateTime SubmissionDate { get; init; }
	public DateTime ExpirationDate { get; init; }
	public int ViewCount { get; init; }
	public int InterestedEmployers { get; init; }
	public IImmutableList<string> Documents { get; init; }
	public IImmutableList<string> RequiredSkills { get; init; }
	public string? PreferredCompanySize { get; init; }
	public string? RemoteWorkPreference { get; init; }
	public IImmutableList<string> LanguageRequirements { get; init; }
	public UserProfile CreatedBy { get; init; }
	public IImmutableList<EmployerResponse> Responses { get; init; }

	// Computed properties
	public string StatusColor => Status switch
	{
		"Pending" => "#FFA500",
		"Under Review" => "#0066CC",
		"Approved" => "#28A745",
		"Rejected" => "#DC3545",
		_ => "#6C757D"
	};

	public string PriorityColor => Priority switch
	{
		"High" => "#DC3545",
		"Medium" => "#FFA500",
		"Low" => "#28A745",
		_ => "#6C757D"
	};

	public bool IsExpired => DateTime.UtcNow > ExpirationDate;
	public bool IsActive => Status == "Pending" || Status == "Under Review";
	public int DaysUntilExpiration => Math.Max(0, (ExpirationDate - DateTime.UtcNow).Days);

	internal TransferRequestData ToData() => new()
	{
		Id = Id,
		UserId = UserId,
		RequestTitle = RequestTitle,
		CurrentLocation = CurrentLocation.ToData(),
		DestinationLocation = DestinationLocation.ToData(),
		CurrentSalaryGrade = CurrentSalaryGrade,
		DesiredSalaryGrade = DesiredSalaryGrade,
		CurrentFinancialGrade = CurrentFinancialGrade,
		DesiredFinancialGrade = DesiredFinancialGrade,
		Industry = Industry,
		JobCategory = JobCategory,
		TransferReason = TransferReason,
		Status = Status,
		Priority = Priority,
		SubmissionDate = SubmissionDate,
		ExpirationDate = ExpirationDate,
		ViewCount = ViewCount,
		InterestedEmployers = InterestedEmployers,
		Documents = Documents.ToList(),
		RequiredSkills = RequiredSkills.ToList(),
		PreferredCompanySize = PreferredCompanySize,
		RemoteWorkPreference = RemoteWorkPreference,
		LanguageRequirements = LanguageRequirements.ToList(),
		CreatedBy = CreatedBy.ToData(),
		Responses = Responses.Select(r => r.ToData()).ToList()
	};
}

// Supporting models
public partial record Location
{
	internal Location(LocationData? locationData)
	{
		City = locationData?.City;
		State = locationData?.State;
		Country = locationData?.Country;
	}

	public string? City { get; init; }
	public string? State { get; init; }
	public string? Country { get; init; }

	internal LocationData ToData() => new()
	{
		City = City,
		State = State,
		Country = Country
	};
}

public partial record UserProfile
{
	internal UserProfile(UserProfileData? userProfileData)
	{
		Id = userProfileData?.Id ?? Guid.Empty;
		FullName = userProfileData?.FullName;
		ProfileImageUrl = userProfileData?.ProfileImageUrl;
		CurrentPosition = userProfileData?.CurrentPosition;
		Experience = userProfileData?.Experience;
	}

	public Guid Id { get; init; }
	public string? FullName { get; init; }
	public string? ProfileImageUrl { get; init; }
	public string? CurrentPosition { get; init; }
	public string? Experience { get; init; }

	internal UserProfileData ToData() => new()
	{
		Id = Id,
		FullName = FullName,
		ProfileImageUrl = ProfileImageUrl,
		CurrentPosition = CurrentPosition,
		Experience = Experience
	};
}

public partial record EmployerResponse
{
	internal EmployerResponse(EmployerResponseData? employerResponseData)
	{
		Id = employerResponseData?.Id;
		EmployerId = employerResponseData?.EmployerId;
		CompanyName = employerResponseData?.CompanyName;
		ResponseDate = employerResponseData?.ResponseDate ?? DateTime.MinValue;
		Status = employerResponseData?.Status;
		Message = employerResponseData?.Message;
		OfferedSalary = employerResponseData?.OfferedSalary;
		InterviewScheduled = employerResponseData?.InterviewScheduled ?? false;
	}

	public string? Id { get; init; }
	public string? EmployerId { get; init; }
	public string? CompanyName { get; init; }
	public DateTime ResponseDate { get; init; }
	public string? Status { get; init; }
	public string? Message { get; init; }
	public string? OfferedSalary { get; init; }
	public bool InterviewScheduled { get; init; }

	internal EmployerResponseData ToData() => new()
	{
		Id = Id,
		EmployerId = EmployerId,
		CompanyName = CompanyName,
		ResponseDate = ResponseDate,
		Status = Status,
		Message = Message,
		OfferedSalary = OfferedSalary,
		InterviewScheduled = InterviewScheduled
	};
}
