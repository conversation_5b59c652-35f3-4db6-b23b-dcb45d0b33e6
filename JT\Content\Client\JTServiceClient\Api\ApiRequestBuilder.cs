// <auto-generated/>
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using JT.Client.Api.JT;
using Microsoft.Kiota.Abstractions;
using Microsoft.Kiota.Abstractions.Extensions;

namespace JT.Client.Api;
/// <summary>
/// Builds and executes requests for operations under \api
/// </summary>
[global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.16.0")]
public partial class ApiRequestBuilder : BaseRequestBuilder
{        /// <summary>The Cookbook property</summary>
    public global::JT.Client.Api.Cookbook.CookbookRequestBuilder Cookbook
    {
        get => new global::JT.Client.Api.Cookbook.CookbookRequestBuilder(PathParameters, RequestAdapter);
    }
    /// <summary>The Notification property</summary>
    public global::JT.Client.Api.Notification.NotificationRequestBuilder Notification
    {
        get => new global::JT.Client.Api.Notification.NotificationRequestBuilder(PathParameters, RequestAdapter);
    }
    /// <summary>The Recipe property</summary>
    public global::JT.Client.Api.Recipe.RecipeRequestBuilder Recipe
    {
        get => new global::JT.Client.Api.Recipe.RecipeRequestBuilder(PathParameters, RequestAdapter);
    }
    /// <summary>The User property</summary>
    public global::JT.Client.Api.User.UserRequestBuilder User
    {
        get => new global::JT.Client.Api.User.UserRequestBuilder(PathParameters, RequestAdapter);
    }
    /// <summary>
    /// Instantiates a new <see cref="global::JT.Client.Api.ApiRequestBuilder"/> and sets the default values.
    /// </summary>
    /// <param name="pathParameters">Path parameters for the request</param>
    /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
    public ApiRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api", pathParameters)
    {
    }
    /// <summary>
    /// Instantiates a new <see cref="global::JT.Client.Api.ApiRequestBuilder"/> and sets the default values.
    /// </summary>
    /// <param name="rawUrl">The raw URL to use for the request builder.</param>
    /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
    public ApiRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api", rawUrl)
    {
    }



    /// <summary>The weatherforecast property</summary>
    public global::JT.Client.Api.JT.JTRequestBuilder Jt
    {
        get => new global::JT.Client.Api.JT.JTRequestBuilder(PathParameters, RequestAdapter);
    }
    ///// <summary>The JT property</summary>
    //public global::JT.Client.Api.JT.JTRequestBuilder Cookbook
    //{
    //    get => new global::JT.Client.Api.JT.JTRequestBuilder(PathParameters, RequestAdapter);
    //}
    ///// <summary>The Notification property</summary>
    //public global::JT.Client.Api.Notification.NotificationRequestBuilder Notification
    //{
    //    get => new global::JT.Client.Api.Notification.NotificationRequestBuilder(PathParameters, RequestAdapter);
    //}
    ///// <summary>The Recipe property</summary>
    //public global::JT.Client.Api.Recipe.RecipeRequestBuilder Recipe
    //{
    //    get => new global::JT.Client.Api.Recipe.RecipeRequestBuilder(PathParameters, RequestAdapter);
    //}
    ///// <summary>The User property</summary>
    //public global::JT.Client.Api.User.UserRequestBuilder User
    //{
    //    get => new global::JT.Client.Api.User.UserRequestBuilder(PathParameters, RequestAdapter);
    //}

    ///// <summary>
    ///// Instantiates a new <see cref="global::JT.Client.Api.ApiRequestBuilder"/> and sets the default values.
    ///// </summary>
    ///// <param name="pathParameters">Path parameters for the request</param>
    ///// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
    //public ApiRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api", pathParameters)
    //{
    //}
    ///// <summary>
    ///// Instantiates a new <see cref="global::JT.Client.Api.ApiRequestBuilder"/> and sets the default values.
    ///// </summary>
    ///// <param name="rawUrl">The raw URL to use for the request builder.</param>
    ///// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
    //public ApiRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api", rawUrl)
    //{
    //}
}
