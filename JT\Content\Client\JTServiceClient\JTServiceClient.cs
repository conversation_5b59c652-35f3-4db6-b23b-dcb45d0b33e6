// <auto-generated/>
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using JT.Client.Api;
using Microsoft.Kiota.Abstractions;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Serialization.Form;
using Microsoft.Kiota.Serialization.Json;
using Microsoft.Kiota.Serialization.Multipart;
using Microsoft.Kiota.Serialization.Text;

namespace JT.Client;
/// <summary>
/// The main entry point of the SDK, exposes the configuration and the fluent API.
/// </summary>
[global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.16.0")]
public partial class JTServiceClient : BaseRequestBuilder
{
    /// <summary>The api property</summary>
    public global::JT.Client.Api.ApiRequestBuilder Api
    {
        get => new global::JT.Client.Api.ApiRequestBuilder(PathParameters, RequestAdapter);
    }
    /// <summary>
    /// Instantiates a new <see cref="global::JT.Client.JTServiceClient"/> and sets the default values.
    /// </summary>
    /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
    public JTServiceClient(IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}", new Dictionary<string, object>())
    {
        ApiClientBuilder.RegisterDefaultSerializer<JsonSerializationWriterFactory>();
        ApiClientBuilder.RegisterDefaultSerializer<TextSerializationWriterFactory>();
        ApiClientBuilder.RegisterDefaultSerializer<FormSerializationWriterFactory>();
        ApiClientBuilder.RegisterDefaultSerializer<MultipartSerializationWriterFactory>();
        ApiClientBuilder.RegisterDefaultDeserializer<JsonParseNodeFactory>();
        ApiClientBuilder.RegisterDefaultDeserializer<TextParseNodeFactory>();
        ApiClientBuilder.RegisterDefaultDeserializer<FormParseNodeFactory>();
        if (string.IsNullOrEmpty(RequestAdapter.BaseUrl))
        {
            RequestAdapter.BaseUrl = "https://localhost:5002";
        }
        PathParameters.TryAdd("baseurl", RequestAdapter.BaseUrl);
    }
}
