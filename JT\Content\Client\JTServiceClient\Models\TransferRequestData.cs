// <auto-generated/>
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System;
namespace JT.Client.Models {
    public class TransferRequestData : IAdditionalDataHolder, IParsable {
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The createdBy property</summary>
        public UserProfileData CreatedBy { get; set; }
        /// <summary>The currentFinancialGrade property</summary>
        public string CurrentFinancialGrade { get; set; }
        /// <summary>The currentLocation property</summary>
        public LocationData CurrentLocation { get; set; }
        /// <summary>The currentSalaryGrade property</summary>
        public string CurrentSalaryGrade { get; set; }
        /// <summary>The desiredFinancialGrade property</summary>
        public string DesiredFinancialGrade { get; set; }
        /// <summary>The desiredSalaryGrade property</summary>
        public string DesiredSalaryGrade { get; set; }
        /// <summary>The destinationLocation property</summary>
        public LocationData DestinationLocation { get; set; }
        /// <summary>The documents property</summary>
        public List<string> Documents { get; set; }
        /// <summary>The expirationDate property</summary>
        public DateTimeOffset? ExpirationDate { get; set; }
        /// <summary>The id property</summary>
        public Guid? Id { get; set; }
        /// <summary>The industry property</summary>
        public string Industry { get; set; }
        /// <summary>The interestedEmployers property</summary>
        public int? InterestedEmployers { get; set; }
        /// <summary>The jobCategory property</summary>
        public string JobCategory { get; set; }
        /// <summary>The languageRequirements property</summary>
        public List<string> LanguageRequirements { get; set; }
        /// <summary>The preferredCompanySize property</summary>
        public string PreferredCompanySize { get; set; }
        /// <summary>The priority property</summary>
        public string Priority { get; set; }
        /// <summary>The remoteWorkPreference property</summary>
        public string RemoteWorkPreference { get; set; }
        /// <summary>The requestTitle property</summary>
        public string RequestTitle { get; set; }
        /// <summary>The requiredSkills property</summary>
        public List<string> RequiredSkills { get; set; }
        /// <summary>The responses property</summary>
        public List<EmployerResponseData> Responses { get; set; }
        /// <summary>The status property</summary>
        public string Status { get; set; }
        /// <summary>The submissionDate property</summary>
        public DateTimeOffset? SubmissionDate { get; set; }
        /// <summary>The transferReason property</summary>
        public string TransferReason { get; set; }
        /// <summary>The userId property</summary>
        public Guid? UserId { get; set; }
        /// <summary>The viewCount property</summary>
        public int? ViewCount { get; set; }
        /// <summary>
        /// Instantiates a new TransferRequestData and sets the default values.
        /// </summary>
        public TransferRequestData() {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static TransferRequestData CreateFromDiscriminatorValue(IParseNode parseNode) {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new TransferRequestData();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers() {
            return new Dictionary<string, Action<IParseNode>> {
                {"createdBy", n => { CreatedBy = n.GetObjectValue<UserProfileData>(UserProfileData.CreateFromDiscriminatorValue); } },
                {"currentFinancialGrade", n => { CurrentFinancialGrade = n.GetStringValue(); } },
                {"currentLocation", n => { CurrentLocation = n.GetObjectValue<LocationData>(LocationData.CreateFromDiscriminatorValue); } },
                {"currentSalaryGrade", n => { CurrentSalaryGrade = n.GetStringValue(); } },
                {"desiredFinancialGrade", n => { DesiredFinancialGrade = n.GetStringValue(); } },
                {"desiredSalaryGrade", n => { DesiredSalaryGrade = n.GetStringValue(); } },
                {"destinationLocation", n => { DestinationLocation = n.GetObjectValue<LocationData>(LocationData.CreateFromDiscriminatorValue); } },
                {"documents", n => { Documents = n.GetCollectionOfPrimitiveValues<string>()?.ToList(); } },
                {"expirationDate", n => { ExpirationDate = n.GetDateTimeOffsetValue(); } },
                {"id", n => { Id = n.GetGuidValue(); } },
                {"industry", n => { Industry = n.GetStringValue(); } },
                {"interestedEmployers", n => { InterestedEmployers = n.GetIntValue(); } },
                {"jobCategory", n => { JobCategory = n.GetStringValue(); } },
                {"languageRequirements", n => { LanguageRequirements = n.GetCollectionOfPrimitiveValues<string>()?.ToList(); } },
                {"preferredCompanySize", n => { PreferredCompanySize = n.GetStringValue(); } },
                {"priority", n => { Priority = n.GetStringValue(); } },
                {"remoteWorkPreference", n => { RemoteWorkPreference = n.GetStringValue(); } },
                {"requestTitle", n => { RequestTitle = n.GetStringValue(); } },
                {"requiredSkills", n => { RequiredSkills = n.GetCollectionOfPrimitiveValues<string>()?.ToList(); } },
                {"responses", n => { Responses = n.GetCollectionOfObjectValues<EmployerResponseData>(EmployerResponseData.CreateFromDiscriminatorValue)?.ToList(); } },
                {"status", n => { Status = n.GetStringValue(); } },
                {"submissionDate", n => { SubmissionDate = n.GetDateTimeOffsetValue(); } },
                {"transferReason", n => { TransferReason = n.GetStringValue(); } },
                {"userId", n => { UserId = n.GetGuidValue(); } },
                {"viewCount", n => { ViewCount = n.GetIntValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer) {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteObjectValue<UserProfileData>("createdBy", CreatedBy);
            writer.WriteStringValue("currentFinancialGrade", CurrentFinancialGrade);
            writer.WriteObjectValue<LocationData>("currentLocation", CurrentLocation);
            writer.WriteStringValue("currentSalaryGrade", CurrentSalaryGrade);
            writer.WriteStringValue("desiredFinancialGrade", DesiredFinancialGrade);
            writer.WriteStringValue("desiredSalaryGrade", DesiredSalaryGrade);
            writer.WriteObjectValue<LocationData>("destinationLocation", DestinationLocation);
            writer.WriteCollectionOfPrimitiveValues<string>("documents", Documents);
            writer.WriteDateTimeOffsetValue("expirationDate", ExpirationDate);
            writer.WriteGuidValue("id", Id);
            writer.WriteStringValue("industry", Industry);
            writer.WriteIntValue("interestedEmployers", InterestedEmployers);
            writer.WriteStringValue("jobCategory", JobCategory);
            writer.WriteCollectionOfPrimitiveValues<string>("languageRequirements", LanguageRequirements);
            writer.WriteStringValue("preferredCompanySize", PreferredCompanySize);
            writer.WriteStringValue("priority", Priority);
            writer.WriteStringValue("remoteWorkPreference", RemoteWorkPreference);
            writer.WriteStringValue("requestTitle", RequestTitle);
            writer.WriteCollectionOfPrimitiveValues<string>("requiredSkills", RequiredSkills);
            writer.WriteCollectionOfObjectValues<EmployerResponseData>("responses", Responses);
            writer.WriteStringValue("status", Status);
            writer.WriteDateTimeOffsetValue("submissionDate", SubmissionDate);
            writer.WriteStringValue("transferReason", TransferReason);
            writer.WriteGuidValue("userId", UserId);
            writer.WriteIntValue("viewCount", ViewCount);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
