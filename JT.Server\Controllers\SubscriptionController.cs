using JT.Server.Entities;
using Microsoft.AspNetCore.Mvc;

namespace JT.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SubscriptionController : ChefsControllerBase
{
	private readonly ILogger<SubscriptionController> _logger;

	public SubscriptionController(ILogger<SubscriptionController> logger)
	{
		_logger = logger;
	}

	[HttpGet("plans")]
	public async Task<ActionResult<IEnumerable<SubscriptionPlanData>>> GetSubscriptionPlans()
	{
		try
		{
			var plans = await GetMockData<SubscriptionPlanData>("SubscriptionPlans.json");
			return Ok(plans);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting subscription plans");
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("plans/{planId}")]
	public async Task<ActionResult<SubscriptionPlanData>> GetSubscriptionPlan(string planId)
	{
		try
		{
			var plans = await GetMockData<SubscriptionPlanData>("SubscriptionPlans.json");
			var plan = plans.FirstOrDefault(p => p.Id == planId);
			
			if (plan == null)
			{
				return NotFound();
			}

			return Ok(plan);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting subscription plan {PlanId}", planId);
			return StatusCode(500, "Internal server error");
		}
	}
}
