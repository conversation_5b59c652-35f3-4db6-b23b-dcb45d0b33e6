// <auto-generated/>
using System;
using System.Collections.Generic;
using System.IO;
using Microsoft.Kiota.Abstractions;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;

namespace JT.Client.Models;
[global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.16.0")]
#pragma warning disable CS1591
public partial class JT : IAdditionalDataHolder, IParsable
#pragma warning restore CS1591
{
    /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
    public IDictionary<string, object> AdditionalData { get; set; }
    /// <summary>The date property</summary>
    public Date? Date { get; set; }
    /// <summary>The summary property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
    public string? Summary { get; set; }
#nullable restore
#else
    public string Summary { get; set; }
#endif
    /// <summary>The temperatureC property</summary>
    public int? TemperatureC { get; set; }
    /// <summary>
    /// Instantiates a new <see cref="global::JT.Client.Models.JT"/> and sets the default values.
    /// </summary>
    public JT()
    {
        AdditionalData = new Dictionary<string, object>();
    }
    /// <summary>
    /// Creates a new instance of the appropriate class based on discriminator value
    /// </summary>
    /// <returns>A <see cref="global::JT.Client.Models.JT"/></returns>
    /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
    public static global::JT.Client.Models.JT CreateFromDiscriminatorValue(IParseNode parseNode)
    {
        _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
        return new global::JT.Client.Models.JT();
    }
    /// <summary>
    /// The deserialization information for the current model
    /// </summary>
    /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
    public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
    {
        return new Dictionary<string, Action<IParseNode>>
        {
            { "date", n => { Date = n.GetDateValue(); } },
            { "summary", n => { Summary = n.GetStringValue(); } },
            { "temperatureC", n => { TemperatureC = n.GetIntValue(); } },
        };
    }
    /// <summary>
    /// Serializes information the current object
    /// </summary>
    /// <param name="writer">Serialization writer to use to serialize this model</param>
    public virtual void Serialize(ISerializationWriter writer)
    {
        _ = writer ?? throw new ArgumentNullException(nameof(writer));
        writer.WriteDateValue("date", Date);
        writer.WriteStringValue("summary", Summary);
        writer.WriteIntValue("temperatureC", TemperatureC);
        writer.WriteAdditionalData(AdditionalData);
    }
}
