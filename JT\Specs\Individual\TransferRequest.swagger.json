{"openapi": "3.0.1", "info": {"title": "Transfer Request API", "version": "v1", "description": "API for job transfer request management in Omani Job Transfer Application"}, "servers": [{"url": "https://localhost:5002", "description": "Local development server"}], "paths": {"/api/transferrequest": {"get": {"summary": "Get all transfer requests", "operationId": "GetTransferRequests", "tags": ["TransferRequest"], "responses": {"200": {"description": "A list of transfer requests", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransferRequest"}}}}}}}, "post": {"summary": "Create a new transfer request", "operationId": "CreateTransferRequest", "tags": ["TransferRequest"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequest"}}}}, "responses": {"201": {"description": "Transfer request created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequest"}}}}}}}, "/api/transferrequest/{id}": {"get": {"summary": "Get transfer request by ID", "operationId": "GetTransferRequestById", "tags": ["TransferRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Transfer request found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequest"}}}}, "404": {"description": "Transfer request not found"}}}, "put": {"summary": "Update transfer request", "operationId": "UpdateTransferRequest", "tags": ["TransferRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequest"}}}}, "responses": {"200": {"description": "Transfer request updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequest"}}}}}}, "delete": {"summary": "Delete transfer request", "operationId": "DeleteTransferRequest", "tags": ["TransferRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"204": {"description": "Transfer request deleted successfully"}}}}}, "components": {"schemas": {"TransferRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "requestTitle": {"type": "string"}, "currentLocation": {"$ref": "#/components/schemas/Location"}, "destinationLocation": {"$ref": "#/components/schemas/Location"}, "currentSalaryGrade": {"type": "string"}, "desiredSalaryGrade": {"type": "string"}, "currentFinancialGrade": {"type": "string"}, "desiredFinancialGrade": {"type": "string"}, "industry": {"type": "string"}, "jobCategory": {"type": "string"}, "transferReason": {"type": "string"}, "status": {"type": "string", "enum": ["pending", "approved", "rejected", "expired"]}, "priority": {"type": "string", "enum": ["low", "normal", "high", "urgent"]}, "submissionDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}, "viewCount": {"type": "integer"}, "interestedEmployers": {"type": "integer"}, "documents": {"type": "array", "items": {"type": "string"}}, "requiredSkills": {"type": "array", "items": {"type": "string"}}, "preferredCompanySize": {"type": "string"}, "remoteWorkPreference": {"type": "string"}, "languageRequirements": {"type": "array", "items": {"type": "string"}}, "createdBy": {"$ref": "#/components/schemas/UserProfile"}, "responses": {"type": "array", "items": {"$ref": "#/components/schemas/EmployerResponse"}}}, "required": ["userId", "requestTitle", "currentLocation", "destinationLocation", "industry"]}, "Location": {"type": "object", "properties": {"city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}}}, "UserProfile": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "fullName": {"type": "string"}, "profileImageUrl": {"type": "string"}, "currentPosition": {"type": "string"}, "experience": {"type": "string"}}}, "EmployerResponse": {"type": "object", "properties": {"id": {"type": "string"}, "employerId": {"type": "string"}, "message": {"type": "string"}, "offerDetails": {"type": "string"}, "responseDate": {"type": "string", "format": "date-time"}}}}}}