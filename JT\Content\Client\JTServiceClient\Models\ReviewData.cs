// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace JT.Client.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class ReviewData : IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The createdBy property</summary>
        public Guid? CreatedBy { get; set; }
        /// <summary>The date property</summary>
        public DateTimeOffset? Date { get; set; }
        /// <summary>The description property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Description { get; set; }
#nullable restore
#else
        public string Description { get; set; }
#endif
        /// <summary>The dislikes property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<Guid?>? Dislikes { get; set; }
#nullable restore
#else
        public List<Guid?> Dislikes { get; set; }
#endif
        /// <summary>The id property</summary>
        public Guid? Id { get; set; }
        /// <summary>The likes property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<Guid?>? Likes { get; set; }
#nullable restore
#else
        public List<Guid?> Likes { get; set; }
#endif
        /// <summary>The publisherName property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? PublisherName { get; set; }
#nullable restore
#else
        public string PublisherName { get; set; }
#endif
        /// <summary>The recipeId property</summary>
        public Guid? RecipeId { get; set; }
        /// <summary>The urlAuthorImage property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? UrlAuthorImage { get; set; }
#nullable restore
#else
        public string UrlAuthorImage { get; set; }
#endif
        /// <summary>The userLike property</summary>
        public bool? UserLike { get; set; }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::JT.Client.Models.ReviewData"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::JT.Client.Models.ReviewData CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::JT.Client.Models.ReviewData();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "createdBy", n => { CreatedBy = n.GetGuidValue(); } },
                { "date", n => { Date = n.GetDateTimeOffsetValue(); } },
                { "description", n => { Description = n.GetStringValue(); } },
                { "dislikes", n => { Dislikes = n.GetCollectionOfPrimitiveValues<Guid?>()?.AsList(); } },
                { "id", n => { Id = n.GetGuidValue(); } },
                { "likes", n => { Likes = n.GetCollectionOfPrimitiveValues<Guid?>()?.AsList(); } },
                { "publisherName", n => { PublisherName = n.GetStringValue(); } },
                { "recipeId", n => { RecipeId = n.GetGuidValue(); } },
                { "urlAuthorImage", n => { UrlAuthorImage = n.GetStringValue(); } },
                { "userLike", n => { UserLike = n.GetBoolValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteGuidValue("createdBy", CreatedBy);
            writer.WriteDateTimeOffsetValue("date", Date);
            writer.WriteStringValue("description", Description);
            writer.WriteCollectionOfPrimitiveValues<Guid?>("dislikes", Dislikes);
            writer.WriteGuidValue("id", Id);
            writer.WriteCollectionOfPrimitiveValues<Guid?>("likes", Likes);
            writer.WriteStringValue("publisherName", PublisherName);
            writer.WriteGuidValue("recipeId", RecipeId);
            writer.WriteStringValue("urlAuthorImage", UrlAuthorImage);
            writer.WriteBoolValue("userLike", UserLike);
        }
    }
}
#pragma warning restore CS0618
