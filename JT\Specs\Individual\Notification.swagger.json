{"openapi": "3.0.1", "info": {"title": "Notification & Search API", "version": "v1", "description": "API for notifications and saved searches in Omani Job Transfer Application"}, "servers": [{"url": "https://localhost:5002", "description": "Local development server"}], "paths": {"/api/notification/user/{userId}": {"get": {"summary": "Get user notifications", "operationId": "GetUserNotifications", "tags": ["Notification"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User notifications", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}}}}}}}}, "/api/savedsearch/user/{userId}": {"get": {"summary": "Get user saved searches", "operationId": "GetUserSavedSearches", "tags": ["SavedSearch"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User saved searches", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SavedSearch"}}}}}}}, "post": {"summary": "Save a search for user", "operationId": "SaveSearch", "tags": ["SavedSearch"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavedSearch"}}}}, "responses": {"201": {"description": "Search saved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavedSearch"}}}}}}}}, "components": {"schemas": {"Notification": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string", "format": "uuid"}, "title": {"type": "string"}, "message": {"type": "string"}, "type": {"type": "string", "enum": ["info", "success", "warning", "error", "promotion"]}, "isRead": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "actionUrl": {"type": "string"}, "priority": {"type": "string", "enum": ["low", "normal", "high"]}}, "required": ["id", "userId", "title", "message"]}, "SavedSearch": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string", "format": "uuid"}, "searchName": {"type": "string"}, "searchCriteria": {"type": "object", "properties": {"keywords": {"type": "string"}, "location": {"type": "string"}, "jobCategory": {"type": "string"}, "salaryRange": {"type": "object", "properties": {"min": {"type": "number"}, "max": {"type": "number"}}}, "experienceLevel": {"type": "string"}, "workType": {"type": "string"}}}, "alertsEnabled": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "lastUsed": {"type": "string", "format": "date-time"}}, "required": ["id", "userId", "searchName", "searchCriteria"]}}}}