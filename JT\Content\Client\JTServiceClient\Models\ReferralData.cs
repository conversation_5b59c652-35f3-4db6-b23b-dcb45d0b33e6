// <auto-generated/>
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System;
namespace JT.Client.Models {
    public class ReferralData : IAdditionalDataHolder, IParsable {
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The bonusPaid property</summary>
        public bool? BonusPaid { get; set; }
        /// <summary>The completedAt property</summary>
        public DateTimeOffset? CompletedAt { get; set; }
        /// <summary>The createdAt property</summary>
        public DateTimeOffset? CreatedAt { get; set; }
        /// <summary>The id property</summary>
        public string Id { get; set; }
        /// <summary>The invitationSent property</summary>
        public bool? InvitationSent { get; set; }
        /// <summary>The referralCode property</summary>
        public string ReferralCode { get; set; }
        /// <summary>The referredUserEmail property</summary>
        public string ReferredUserEmail { get; set; }
        /// <summary>The referredUserId property</summary>
        public Guid? ReferredUserId { get; set; }
        /// <summary>The referredUserName property</summary>
        public string ReferredUserName { get; set; }
        /// <summary>The referrerId property</summary>
        public Guid? ReferrerId { get; set; }
        /// <summary>The status property</summary>
        public string Status { get; set; }
        /// <summary>The tokensEarned property</summary>
        public int? TokensEarned { get; set; }
        /// <summary>
        /// Instantiates a new ReferralData and sets the default values.
        /// </summary>
        public ReferralData() {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static ReferralData CreateFromDiscriminatorValue(IParseNode parseNode) {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new ReferralData();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers() {
            return new Dictionary<string, Action<IParseNode>> {
                {"bonusPaid", n => { BonusPaid = n.GetBoolValue(); } },
                {"completedAt", n => { CompletedAt = n.GetDateTimeOffsetValue(); } },
                {"createdAt", n => { CreatedAt = n.GetDateTimeOffsetValue(); } },
                {"id", n => { Id = n.GetStringValue(); } },
                {"invitationSent", n => { InvitationSent = n.GetBoolValue(); } },
                {"referralCode", n => { ReferralCode = n.GetStringValue(); } },
                {"referredUserEmail", n => { ReferredUserEmail = n.GetStringValue(); } },
                {"referredUserId", n => { ReferredUserId = n.GetGuidValue(); } },
                {"referredUserName", n => { ReferredUserName = n.GetStringValue(); } },
                {"referrerId", n => { ReferrerId = n.GetGuidValue(); } },
                {"status", n => { Status = n.GetStringValue(); } },
                {"tokensEarned", n => { TokensEarned = n.GetIntValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer) {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteBoolValue("bonusPaid", BonusPaid);
            writer.WriteDateTimeOffsetValue("completedAt", CompletedAt);
            writer.WriteDateTimeOffsetValue("createdAt", CreatedAt);
            writer.WriteStringValue("id", Id);
            writer.WriteBoolValue("invitationSent", InvitationSent);
            writer.WriteStringValue("referralCode", ReferralCode);
            writer.WriteStringValue("referredUserEmail", ReferredUserEmail);
            writer.WriteGuidValue("referredUserId", ReferredUserId);
            writer.WriteStringValue("referredUserName", ReferredUserName);
            writer.WriteGuidValue("referrerId", ReferrerId);
            writer.WriteStringValue("status", Status);
            writer.WriteIntValue("tokensEarned", TokensEarned);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
