// <auto-generated/>
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace JT.Client.Models
{
	[global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.16.0")]
#pragma warning disable CS1591
	public partial class RecipeData : IParsable
#pragma warning restore CS1591
	{
		/// <summary>The calories property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
		public string? Calories { get; set; }
#nullable restore
#else
        public string Calories { get; set; }
#endif
		/// <summary>The category property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
		public global::JT.Client.Models.CategoryData? Category { get; set; }
#nullable restore
#else
        public global::JT.Client.Models.CategoryData Category { get; set; }
#endif
		/// <summary>The cookTime property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
		public global::JT.Client.Models.TimeSpanObject? CookTime { get; set; }
#nullable restore
#else
        public global::JT.Client.Models.TimeSpanObject CookTime { get; set; }
#endif
		/// <summary>The date property</summary>
		public DateTimeOffset? Date { get; set; }
		/// <summary>The details property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
		public string? Details { get; set; }
#nullable restore
#else
        public string Details { get; set; }
#endif
		/// <summary>The difficulty property</summary>
		public int? Difficulty { get; set; }
		/// <summary>The id property</summary>
		public Guid? Id { get; set; }
		/// <summary>The imageUrl property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
		public string? ImageUrl { get; set; }
#nullable restore
#else
        public string ImageUrl { get; set; }
#endif
		/// <summary>The ingredients property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
		public List<global::JT.Client.Models.IngredientData>? Ingredients { get; set; }
#nullable restore
#else
        public List<global::JT.Client.Models.IngredientData> Ingredients { get; set; }
#endif
		/// <summary>The isFavorite property</summary>
		public bool? IsFavorite { get; set; }
		/// <summary>The name property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
		public string? Name { get; set; }
#nullable restore
#else
        public string Name { get; set; }
#endif
		/// <summary>The nutrition property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
		public global::JT.Client.Models.NutritionData? Nutrition { get; set; }
#nullable restore
#else
        public global::JT.Client.Models.NutritionData Nutrition { get; set; }
#endif
		/// <summary>The reviews property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
		public List<global::JT.Client.Models.ReviewData>? Reviews { get; set; }
#nullable restore
#else
        public List<global::JT.Client.Models.ReviewData> Reviews { get; set; }
#endif
		/// <summary>The serves property</summary>
		public int? Serves { get; set; }
		/// <summary>The steps property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
		public List<global::JT.Client.Models.StepData>? Steps { get; set; }
#nullable restore
#else
        public List<global::JT.Client.Models.StepData> Steps { get; set; }
#endif
		/// <summary>The userId property</summary>
		public Guid? UserId { get; set; }
		/// <summary>
		/// Creates a new instance of the appropriate class based on discriminator value
		/// </summary>
		/// <returns>A <see cref="global::JT.Client.Models.RecipeData"/></returns>
		/// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
		public static global::JT.Client.Models.RecipeData CreateFromDiscriminatorValue(IParseNode parseNode)
		{
			_ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
			return new global::JT.Client.Models.RecipeData();
		}
		/// <summary>
		/// The deserialization information for the current model
		/// </summary>
		/// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
		public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
		{
			return new Dictionary<string, Action<IParseNode>>
			{
				{ "calories", n => { Calories = n.GetStringValue(); } },
				{ "category", n => { Category = n.GetObjectValue<global::JT.Client.Models.CategoryData>(global::JT.Client.Models.CategoryData.CreateFromDiscriminatorValue); } },
				{ "cookTime", n => { CookTime = n.GetObjectValue<global::JT.Client.Models.TimeSpanObject>(global::JT.Client.Models.TimeSpanObject.CreateFromDiscriminatorValue); } },
				{ "date", n => { Date = n.GetDateTimeOffsetValue(); } },
				{ "details", n => { Details = n.GetStringValue(); } },
				{ "difficulty", n => { Difficulty = n.GetIntValue(); } },
				{ "id", n => { Id = n.GetGuidValue(); } },
				{ "imageUrl", n => { ImageUrl = n.GetStringValue(); } },
				{ "ingredients", n => { Ingredients = n.GetCollectionOfObjectValues<global::JT.Client.Models.IngredientData>(global::JT.Client.Models.IngredientData.CreateFromDiscriminatorValue)?.AsList(); } },
				{ "isFavorite", n => { IsFavorite = n.GetBoolValue(); } },
				{ "name", n => { Name = n.GetStringValue(); } },
				{ "nutrition", n => { Nutrition = n.GetObjectValue<global::JT.Client.Models.NutritionData>(global::JT.Client.Models.NutritionData.CreateFromDiscriminatorValue); } },
				{ "reviews", n => { Reviews = n.GetCollectionOfObjectValues<global::JT.Client.Models.ReviewData>(global::JT.Client.Models.ReviewData.CreateFromDiscriminatorValue)?.AsList(); } },
				{ "serves", n => { Serves = n.GetIntValue(); } },
				{ "steps", n => { Steps = n.GetCollectionOfObjectValues<global::JT.Client.Models.StepData>(global::JT.Client.Models.StepData.CreateFromDiscriminatorValue)?.AsList(); } },
				{ "userId", n => { UserId = n.GetGuidValue(); } },
			};
		}
		/// <summary>
		/// Serializes information the current object
		/// </summary>
		/// <param name="writer">Serialization writer to use to serialize this model</param>
		public virtual void Serialize(ISerializationWriter writer)
		{
			_ = writer ?? throw new ArgumentNullException(nameof(writer));
			writer.WriteStringValue("calories", Calories);
			writer.WriteObjectValue<global::JT.Client.Models.CategoryData>("category", Category);
			writer.WriteObjectValue<global::JT.Client.Models.TimeSpanObject>("cookTime", CookTime);
			writer.WriteDateTimeOffsetValue("date", Date);
			writer.WriteStringValue("details", Details);
			writer.WriteIntValue("difficulty", Difficulty);
			writer.WriteGuidValue("id", Id);
			writer.WriteStringValue("imageUrl", ImageUrl);
			writer.WriteCollectionOfObjectValues<global::JT.Client.Models.IngredientData>("ingredients", Ingredients);
			writer.WriteBoolValue("isFavorite", IsFavorite);
			writer.WriteStringValue("name", Name);
			writer.WriteObjectValue<global::JT.Client.Models.NutritionData>("nutrition", Nutrition);
			writer.WriteCollectionOfObjectValues<global::JT.Client.Models.ReviewData>("reviews", Reviews);
			writer.WriteIntValue("serves", Serves);
			writer.WriteCollectionOfObjectValues<global::JT.Client.Models.StepData>("steps", Steps);
			writer.WriteGuidValue("userId", UserId);
		}
	}
}
