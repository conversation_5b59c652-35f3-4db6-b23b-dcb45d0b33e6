[{"Id": "txn-001", "UserId": "3c896419-e280-40e7-8552-240635566fed", "Amount": 50, "Type": "referral", "Description": "Referral bonus for inviting <PERSON><PERSON>", "ReferenceId": "ref-001", "CreatedAt": "2024-01-15T10:30:00Z", "BalanceAfter": 150}, {"Id": "txn-002", "UserId": "3c896419-e280-40e7-8552-240635566fed", "Amount": 25, "Type": "referral", "Description": "Referral bonus for inviting <PERSON>", "ReferenceId": "ref-002", "CreatedAt": "2024-01-10T14:15:00Z", "BalanceAfter": 100}, {"Id": "txn-003", "UserId": "3c896419-e280-40e7-8552-240635566fed", "Amount": -20, "Type": "redemption", "Description": "Redeemed tokens for premium profile boost", "ReferenceId": "boost-001", "CreatedAt": "2024-01-08T09:45:00Z", "BalanceAfter": 75}, {"Id": "txn-004", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Amount": 100, "Type": "purchase", "Description": "Token bonus for Diamond subscription purchase", "ReferenceId": "pay-001", "CreatedAt": "2024-01-20T16:20:00Z", "BalanceAfter": 320}, {"Id": "txn-005", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "Amount": 75, "Type": "referral", "Description": "Referral bonus for inviting 3 friends", "ReferenceId": "ref-003", "CreatedAt": "2024-01-18T11:30:00Z", "BalanceAfter": 220}, {"Id": "txn-006", "UserId": "56f7e50b-bcc1-4d74-b9eb-c861fc17dbcc", "Amount": 25, "Type": "bonus", "Description": "Welcome bonus for completing profile", "ReferenceId": "welcome-001", "CreatedAt": "2024-01-16T12:00:00Z", "BalanceAfter": 85}, {"Id": "txn-007", "UserId": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "Amount": 15, "Type": "bonus", "Description": "Profile completion bonus", "ReferenceId": "profile-001", "CreatedAt": "2024-01-17T15:45:00Z", "BalanceAfter": 45}]