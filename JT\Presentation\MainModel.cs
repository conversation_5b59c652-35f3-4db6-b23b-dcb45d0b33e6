using JT.Business.Models;
using JT.Business.Services.Users;
using JT.Business.Services.Notifications;
using JT.Models;

namespace JT.Presentation;

public partial record MainModel
{
	private readonly INavigator _navigator;
	private readonly IUserService _userService;
	private readonly INotificationService _notificationService;
	private readonly IMessenger _messenger;

	public MainModel(
		INavigator navigator,
		IUserService userService,
		INotificationService notificationService,
		IMessenger messenger)
	{
		_navigator = navigator;
		_userService = userService;
		_notificationService = notificationService;
		_messenger = messenger;
	}

	public IFeed<User> CurrentUser => _userService.User;

	public IListFeed<Notification> Notifications => ListFeed.Async(GetUserNotifications);

	public IState<int> UnreadNotificationCount => State.Async(this, GetUnreadNotificationCount);

	private async ValueTask<IImmutableList<Notification>> GetUserNotifications(CancellationToken ct)
	{
		var user = await _userService.GetCurrent(ct);
		if (user == null) return ImmutableList<Notification>.Empty;

		return await _notificationService.GetUserNotifications(user.Id, ct);
	}

	private async ValueTask<int> GetUnreadNotificationCount(CancellationToken ct)
	{
		var notifications = await GetUserNotifications(ct);
		return notifications.Count(n => !n.Read);
	}

	public async ValueTask NavigateToHome(CancellationToken ct) =>
		await _navigator.NavigateRouteAsync(this, route: "/Main/Home", cancellation: ct);

	public async ValueTask NavigateToSearch(CancellationToken ct) =>
		await _navigator.NavigateRouteAsync(this, route: "/Main/Search", cancellation: ct);

	public async ValueTask NavigateToMyTransfers(CancellationToken ct) =>
		await _navigator.NavigateRouteAsync(this, route: "/Main/MyTransfers", cancellation: ct);

	public async ValueTask NavigateToSubscriptions(CancellationToken ct) =>
		await _navigator.NavigateRouteAsync(this, route: "/Main/Subscriptions", cancellation: ct);

	public async ValueTask NavigateToTokens(CancellationToken ct) =>
		await _navigator.NavigateRouteAsync(this, route: "/Main/Tokens", cancellation: ct);

	public async ValueTask NavigateToProfile(CancellationToken ct) =>
		await _navigator.NavigateRouteAsync(this, route: "/Main/Profile", cancellation: ct);

	public async ValueTask NavigateToNotifications(CancellationToken ct) =>
		await _navigator.NavigateRouteAsync(this, route: "/Main/Notifications", cancellation: ct);

	public async ValueTask MarkNotificationAsRead(Notification notification, CancellationToken ct)
	{
		if (!notification.Read)
		{
			await _notificationService.MarkAsRead(notification.Id, ct);
		}
	}

	public async ValueTask Logout(CancellationToken ct)
	{
		// Clear user session and navigate to login
		await _navigator.NavigateRouteAsync(this, route: "/Login", cancellation: ct);
	}
}



//public partial record MainModel
//{
//    private INavigator _navigator;

//    public MainModel(
//        IStringLocalizer localizer,
//        IOptions<AppConfig> appInfo,
//        IAuthenticationService authentication,
//        INavigator navigator)
//    {
//        _navigator = navigator;
//        _authentication = authentication;
//        Title = "Main";
//        Title += $" - {localizer["ApplicationName"]}";
//        Title += $" - {appInfo?.Value?.Environment}";
//    }

//    public string? Title { get; }

//    public IState<string> Name => State<string>.Value(this, () => string.Empty);

//    public async Task GoToSecond()
//    {
//        var name = await Name;
//        await _navigator.NavigateViewModelAsync<SecondModel>(this, data: new Entity(name!));
//    }

//    public async ValueTask Logout(CancellationToken token)
//    {
//        await _authentication.LogoutAsync(token);
//    }

//    private IAuthenticationService _authentication;
//}
