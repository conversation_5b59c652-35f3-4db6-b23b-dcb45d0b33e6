// <auto-generated/>
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System;
namespace JT.Client.Models {
    public class UserProfileData : IAdditionalDataHolder, IParsable {
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The currentPosition property</summary>
        public string CurrentPosition { get; set; }
        /// <summary>The experience property</summary>
        public string Experience { get; set; }
        /// <summary>The fullName property</summary>
        public string FullName { get; set; }
        /// <summary>The id property</summary>
        public Guid? Id { get; set; }
        /// <summary>The profileImageUrl property</summary>
        public string ProfileImageUrl { get; set; }
        /// <summary>
        /// Instantiates a new UserProfileData and sets the default values.
        /// </summary>
        public UserProfileData() {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static UserProfileData CreateFromDiscriminatorValue(IParseNode parseNode) {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new UserProfileData();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers() {
            return new Dictionary<string, Action<IParseNode>> {
                {"currentPosition", n => { CurrentPosition = n.GetStringValue(); } },
                {"experience", n => { Experience = n.GetStringValue(); } },
                {"fullName", n => { FullName = n.GetStringValue(); } },
                {"id", n => { Id = n.GetGuidValue(); } },
                {"profileImageUrl", n => { ProfileImageUrl = n.GetStringValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer) {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("currentPosition", CurrentPosition);
            writer.WriteStringValue("experience", Experience);
            writer.WriteStringValue("fullName", FullName);
            writer.WriteGuidValue("id", Id);
            writer.WriteStringValue("profileImageUrl", ProfileImageUrl);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
