// <auto-generated/>
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;

namespace JT.Client.Models
{
	[global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.16.0")]
#pragma warning disable CS1591
	public partial class NotificationData : IParsable
#pragma warning restore CS1591
	{
		/// <summary>The id property</summary>
		public Guid? Id { get; set; }

		/// <summary>The title property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
		public string? Title { get; set; }
#nullable restore
#else
		public string Title { get; set; }
#endif

		/// <summary>The description property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
		public string? Description { get; set; }
#nullable restore
#else
		public string Description { get; set; }
#endif

		/// <summary>The date property</summary>
		public DateTimeOffset? Date { get; set; }

		/// <summary>The isRead property</summary>
		public bool? IsRead { get; set; }

		/// <summary>
		/// Creates a new instance of the appropriate class based on discriminator value
		/// </summary>
		/// <returns>A <see cref="global::JT.Client.Models.NotificationData"/></returns>
		/// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
		public static global::JT.Client.Models.NotificationData CreateFromDiscriminatorValue(IParseNode parseNode)
		{
			_ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
			return new global::JT.Client.Models.NotificationData();
		}

		/// <summary>
		/// The deserialization information for the current model
		/// </summary>
		/// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
		public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
		{
			return new Dictionary<string, Action<IParseNode>>
			{
				{ "id", n => { Id = n.GetGuidValue(); } },
				{ "title", n => { Title = n.GetStringValue(); } },
				{ "description", n => { Description = n.GetStringValue(); } },
				{ "date", n => { Date = n.GetDateTimeOffsetValue(); } },
				{ "read", n => { IsRead = n.GetBoolValue(); } },
			};
		}

		/// <summary>
		/// Serializes information the current object
		/// </summary>
		/// <param name="writer">Serialization writer to use to serialize this model</param>
		public virtual void Serialize(ISerializationWriter writer)
		{
			_ = writer ?? throw new ArgumentNullException(nameof(writer));
			writer.WriteGuidValue("id", Id);
			writer.WriteStringValue("title", Title);
			writer.WriteStringValue("description", Description);
			writer.WriteDateTimeOffsetValue("date", Date);
			writer.WriteBoolValue("read", IsRead);
		}
	}
}
