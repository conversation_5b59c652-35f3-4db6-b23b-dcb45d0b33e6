// <auto-generated/>
#pragma warning disable CS0618
using JT.Client.Api.Recipe.Categories;
using JT.Client.Api.Recipe.Count;
using JT.Client.Api.Recipe.Favorited;
using JT.Client.Api.Recipe.Item;
using JT.Client.Api.Recipe.Popular;
using JT.Client.Api.Recipe.Review;
using JT.Client.Api.Recipe.Trending;
using JT.Client.Models;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Threading;
using System;
namespace JT.Client.Api.Recipe
{
    /// <summary>
    /// Builds and executes requests for operations under \api\Recipe
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class RecipeRequestBuilder : BaseRequestBuilder
    {
        /// <summary>The categories property</summary>
        public global::JT.Client.Api.Recipe.Categories.CategoriesRequestBuilder Categories
        {
            get => new global::JT.Client.Api.Recipe.Categories.CategoriesRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The count property</summary>
        public global::JT.Client.Api.Recipe.Count.CountRequestBuilder Count
        {
            get => new global::JT.Client.Api.Recipe.Count.CountRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The favorited property</summary>
        public global::JT.Client.Api.Recipe.Favorited.FavoritedRequestBuilder Favorited
        {
            get => new global::JT.Client.Api.Recipe.Favorited.FavoritedRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The popular property</summary>
        public global::JT.Client.Api.Recipe.Popular.PopularRequestBuilder Popular
        {
            get => new global::JT.Client.Api.Recipe.Popular.PopularRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The review property</summary>
        public global::JT.Client.Api.Recipe.Review.ReviewRequestBuilder Review
        {
            get => new global::JT.Client.Api.Recipe.Review.ReviewRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The trending property</summary>
        public global::JT.Client.Api.Recipe.Trending.TrendingRequestBuilder Trending
        {
            get => new global::JT.Client.Api.Recipe.Trending.TrendingRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>Gets an item from the JT.Client.api.Recipe.item collection</summary>
        /// <param name="position">Unique identifier of the item</param>
        /// <returns>A <see cref="global::JT.Client.Api.Recipe.Item.WithRecipeItemRequestBuilder"/></returns>
        public global::JT.Client.Api.Recipe.Item.WithRecipeItemRequestBuilder this[Guid position]
        {
            get
            {
                var urlTplParams = new Dictionary<string, object>(PathParameters);
                urlTplParams.Add("recipeId", position);
                return new global::JT.Client.Api.Recipe.Item.WithRecipeItemRequestBuilder(urlTplParams, RequestAdapter);
            }
        }
        /// <summary>Gets an item from the JT.Client.api.Recipe.item collection</summary>
        /// <param name="position">Unique identifier of the item</param>
        /// <returns>A <see cref="global::JT.Client.Api.Recipe.Item.WithRecipeItemRequestBuilder"/></returns>
        [Obsolete("This indexer is deprecated and will be removed in the next major version. Use the one with the typed parameter instead.")]
        public global::JT.Client.Api.Recipe.Item.WithRecipeItemRequestBuilder this[string position]
        {
            get
            {
                var urlTplParams = new Dictionary<string, object>(PathParameters);
                if (!string.IsNullOrWhiteSpace(position)) urlTplParams.Add("recipeId", position);
                return new global::JT.Client.Api.Recipe.Item.WithRecipeItemRequestBuilder(urlTplParams, RequestAdapter);
            }
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Client.Api.Recipe.RecipeRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public RecipeRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/Recipe{?userId*}", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Client.Api.Recipe.RecipeRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public RecipeRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/Recipe{?userId*}", rawUrl)
        {
        }
        /// <returns>A List&lt;global::JT.Client.Models.RecipeData&gt;</returns>
        /// <param name="cancellationToken">Cancellation token to use when cancelling requests</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
        /// <exception cref="global::JT.Client.Models.ProblemDetails">When receiving a 404 status code</exception>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public async Task<List<global::JT.Client.Models.RecipeData>?> GetAsync(Action<RequestConfiguration<DefaultQueryParameters>>? requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#nullable restore
#else
        public async Task<List<global::JT.Client.Models.RecipeData>> GetAsync(Action<RequestConfiguration<DefaultQueryParameters>> requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#endif
            var requestInfo = ToGetRequestInformation(requestConfiguration);
            var errorMapping = new Dictionary<string, ParsableFactory<IParsable>>
            {
                { "404", global::JT.Client.Models.ProblemDetails.CreateFromDiscriminatorValue },
            };
            var collectionResult = await RequestAdapter.SendCollectionAsync<global::JT.Client.Models.RecipeData>(requestInfo, global::JT.Client.Models.RecipeData.CreateFromDiscriminatorValue, errorMapping, cancellationToken).ConfigureAwait(false);
            return collectionResult?.AsList();
        }
        /// <returns>A <see cref="Stream"/></returns>
        /// <param name="body">The request body</param>
        /// <param name="cancellationToken">Cancellation token to use when cancelling requests</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public async Task<Stream?> PostAsync(global::JT.Client.Models.RecipeData body, Action<RequestConfiguration<global::JT.Client.Api.Recipe.RecipeRequestBuilder.RecipeRequestBuilderPostQueryParameters>>? requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#nullable restore
#else
        public async Task<Stream> PostAsync(global::JT.Client.Models.RecipeData body, Action<RequestConfiguration<global::JT.Client.Api.Recipe.RecipeRequestBuilder.RecipeRequestBuilderPostQueryParameters>> requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#endif
            _ = body ?? throw new ArgumentNullException(nameof(body));
            var requestInfo = ToPostRequestInformation(body, requestConfiguration);
            return await RequestAdapter.SendPrimitiveAsync<Stream>(requestInfo, default, cancellationToken).ConfigureAwait(false);
        }
        /// <returns>A <see cref="RequestInformation"/></returns>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public RequestInformation ToGetRequestInformation(Action<RequestConfiguration<DefaultQueryParameters>>? requestConfiguration = default)
        {
#nullable restore
#else
        public RequestInformation ToGetRequestInformation(Action<RequestConfiguration<DefaultQueryParameters>> requestConfiguration = default)
        {
#endif
            var requestInfo = new RequestInformation(Method.GET, UrlTemplate, PathParameters);
            requestInfo.Configure(requestConfiguration);
            requestInfo.Headers.TryAdd("Accept", "application/json");
            return requestInfo;
        }
        /// <returns>A <see cref="RequestInformation"/></returns>
        /// <param name="body">The request body</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public RequestInformation ToPostRequestInformation(global::JT.Client.Models.RecipeData body, Action<RequestConfiguration<global::JT.Client.Api.Recipe.RecipeRequestBuilder.RecipeRequestBuilderPostQueryParameters>>? requestConfiguration = default)
        {
#nullable restore
#else
        public RequestInformation ToPostRequestInformation(global::JT.Client.Models.RecipeData body, Action<RequestConfiguration<global::JT.Client.Api.Recipe.RecipeRequestBuilder.RecipeRequestBuilderPostQueryParameters>> requestConfiguration = default)
        {
#endif
            _ = body ?? throw new ArgumentNullException(nameof(body));
            var requestInfo = new RequestInformation(Method.POST, UrlTemplate, PathParameters);
            requestInfo.Configure(requestConfiguration);
            requestInfo.SetContentFromParsable(RequestAdapter, "application/json", body);
            return requestInfo;
        }
        /// <summary>
        /// Returns a request builder with the provided arbitrary URL. Using this method means any other path or query parameters are ignored.
        /// </summary>
        /// <returns>A <see cref="global::JT.Client.Api.Recipe.RecipeRequestBuilder"/></returns>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        public global::JT.Client.Api.Recipe.RecipeRequestBuilder WithUrl(string rawUrl)
        {
            return new global::JT.Client.Api.Recipe.RecipeRequestBuilder(rawUrl, RequestAdapter);
        }
        /// <summary>
        /// Configuration for the request such as headers, query parameters, and middleware options.
        /// </summary>
        [Obsolete("This class is deprecated. Please use the generic RequestConfiguration class generated by the generator.")]
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class RecipeRequestBuilderGetRequestConfiguration : RequestConfiguration<DefaultQueryParameters>
        {
        }
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        #pragma warning disable CS1591
        public partial class RecipeRequestBuilderPostQueryParameters 
        #pragma warning restore CS1591
        {
            [QueryParameter("userId")]
            public Guid? UserId { get; set; }
        }
        /// <summary>
        /// Configuration for the request such as headers, query parameters, and middleware options.
        /// </summary>
        [Obsolete("This class is deprecated. Please use the generic RequestConfiguration class generated by the generator.")]
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class RecipeRequestBuilderPostRequestConfiguration : RequestConfiguration<global::JT.Client.Api.Recipe.RecipeRequestBuilder.RecipeRequestBuilderPostQueryParameters>
        {
        }
    }
}
#pragma warning restore CS0618
