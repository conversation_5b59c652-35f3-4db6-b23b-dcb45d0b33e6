﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\JT.DataContracts\JT.DataContracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" />
    <PackageReference Include="Swashbuckle.AspNetCore" />
    <PackageReference Include="Uno.Wasm.Bootstrap.Server" />
  </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Include="..\AppData\*.json" LinkBase="AppData" />
    </ItemGroup>
</Project>
