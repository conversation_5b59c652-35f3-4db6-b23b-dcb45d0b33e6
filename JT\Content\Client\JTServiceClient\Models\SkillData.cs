// <auto-generated/>
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System;
namespace JT.Client.Models {
    public class SkillData : IAdditionalDataHolder, IParsable {
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The category property</summary>
        public string Category { get; set; }
        /// <summary>The id property</summary>
        public int? Id { get; set; }
        /// <summary>The industry property</summary>
        public string Industry { get; set; }
        /// <summary>The name property</summary>
        public string Name { get; set; }
        /// <summary>The popularityScore property</summary>
        public int? PopularityScore { get; set; }
        /// <summary>
        /// Instantiates a new SkillData and sets the default values.
        /// </summary>
        public SkillData() {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static SkillData CreateFromDiscriminatorValue(IParseNode parseNode) {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new SkillData();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers() {
            return new Dictionary<string, Action<IParseNode>> {
                {"category", n => { Category = n.GetStringValue(); } },
                {"id", n => { Id = n.GetIntValue(); } },
                {"industry", n => { Industry = n.GetStringValue(); } },
                {"name", n => { Name = n.GetStringValue(); } },
                {"popularityScore", n => { PopularityScore = n.GetIntValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer) {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("category", Category);
            writer.WriteIntValue("id", Id);
            writer.WriteStringValue("industry", Industry);
            writer.WriteStringValue("name", Name);
            writer.WriteIntValue("popularityScore", PopularityScore);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
