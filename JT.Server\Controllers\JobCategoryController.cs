using JT.Server.Entities;
using Microsoft.AspNetCore.Mvc;

namespace JT.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class JobCategoryController : ChefsControllerBase
{
	private readonly ILogger<JobCategoryController> _logger;

	public JobCategoryController(ILogger<JobCategoryController> logger)
	{
		_logger = logger;
	}

	[HttpGet]
	public async Task<ActionResult<IEnumerable<CategoryData>>> GetJobCategories()
	{
		try
		{
			var categories = await GetMockData<CategoryData>("JobCategories.json");
			return Ok(categories);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting job categories");
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("{id}")]
	public async Task<ActionResult<CategoryData>> GetJobCategory(int id)
	{
		try
		{
			var categories = await GetMockData<CategoryData>("JobCategories.json");
			var category = categories.FirstOrDefault(c => c.Id == id);
			
			if (category == null)
			{
				return NotFound();
			}

			return Ok(category);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting job category {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}
}
