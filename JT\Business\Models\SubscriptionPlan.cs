using SubscriptionPlanData = JT.Client.Models.SubscriptionPlanData;

namespace JT.Business.Models;

public partial record SubscriptionPlan
{
	internal SubscriptionPlan(SubscriptionPlanData subscriptionPlanData)
	{
		Id = subscriptionPlanData.Id;
		Name = subscriptionPlanData.Name;
		DisplayName = subscriptionPlanData.DisplayName;
		PriceOMR = subscriptionPlanData.PriceOMR ?? 0;
		PriceDisplay = subscriptionPlanData.PriceDisplay;
		Duration = subscriptionPlanData.Duration;
		Color = subscriptionPlanData.Color;
		IconUrl = subscriptionPlanData.IconUrl;
		MaxDestinations = subscriptionPlanData.MaxDestinations ?? 0;
		PriorityListing = subscriptionPlanData.PriorityListing ?? false;
		PriorityFrequency = subscriptionPlanData.PriorityFrequency;
		WhatsAppAlerts = subscriptionPlanData.WhatsAppAlerts ?? false;
		EmailAlerts = subscriptionPlanData.EmailAlerts ?? false;
		SMSAlerts = subscriptionPlanData.SMSAlerts ?? false;
		InviteLimit = subscriptionPlanData.InviteLimit ?? 0;
		InviteLimitDisplay = subscriptionPlanData.InviteLimitDisplay;
		TokenMultiplier = subscriptionPlanData.TokenMultiplier ?? 1.0;
		AdFreeExperience = subscriptionPlanData.AdFreeExperience ?? false;
		Features = subscriptionPlanData.Features?.ToImmutableList() ?? ImmutableList<string>.Empty;
		ServiceFeePercentage = subscriptionPlanData.ServiceFeePercentage ?? 0;
		PaymentGatewayFeePercentage = subscriptionPlanData.PaymentGatewayFeePercentage ?? 0;
		TotalFeePercentage = subscriptionPlanData.TotalFeePercentage ?? 0;
		IsPopular = subscriptionPlanData.IsPopular ?? false;
		Description = subscriptionPlanData.Description;
	}

	public string? Id { get; init; }
	public string? Name { get; init; }
	public string? DisplayName { get; init; }
	public decimal PriceOMR { get; init; }
	public string? PriceDisplay { get; init; }
	public string? Duration { get; init; }
	public string? Color { get; init; }
	public string? IconUrl { get; init; }
	public int MaxDestinations { get; init; }
	public bool PriorityListing { get; init; }
	public string? PriorityFrequency { get; init; }
	public bool WhatsAppAlerts { get; init; }
	public bool EmailAlerts { get; init; }
	public bool SMSAlerts { get; init; }
	public int InviteLimit { get; init; }
	public string? InviteLimitDisplay { get; init; }
	public double TokenMultiplier { get; init; }
	public bool AdFreeExperience { get; init; }
	public IImmutableList<string> Features { get; init; }
	public double ServiceFeePercentage { get; init; }
	public double PaymentGatewayFeePercentage { get; init; }
	public double TotalFeePercentage { get; init; }
	public bool IsPopular { get; init; }
	public string? Description { get; init; }

	// Computed properties
	public bool IsUnlimitedInvites => InviteLimit == -1;
	public string InvitesDisplay => IsUnlimitedInvites ? "Unlimited" : InviteLimit.ToString();
	
	public decimal ServiceFeeAmount => PriceOMR * (decimal)(ServiceFeePercentage / 100);
	public decimal GatewayFeeAmount => PriceOMR * (decimal)(PaymentGatewayFeePercentage / 100);
	public decimal TotalFeeAmount => ServiceFeeAmount + GatewayFeeAmount;
	public decimal NetAmount => PriceOMR - TotalFeeAmount;

	public string PlanTier => Name?.ToLower() switch
	{
		"bronze" => "Basic",
		"silver" => "Standard",
		"gold" => "Premium",
		"diamond" => "Ultimate",
		_ => "Unknown"
	};

	internal SubscriptionPlanData ToData() => new()
	{
		Id = Id,
		Name = Name,
		DisplayName = DisplayName,
		PriceOMR = PriceOMR,
		PriceDisplay = PriceDisplay,
		Duration = Duration,
		Color = Color,
		IconUrl = IconUrl,
		MaxDestinations = MaxDestinations,
		PriorityListing = PriorityListing,
		PriorityFrequency = PriorityFrequency,
		WhatsAppAlerts = WhatsAppAlerts,
		EmailAlerts = EmailAlerts,
		SMSAlerts = SMSAlerts,
		InviteLimit = InviteLimit,
		InviteLimitDisplay = InviteLimitDisplay,
		TokenMultiplier = TokenMultiplier,
		AdFreeExperience = AdFreeExperience,
		Features = Features.ToList(),
		ServiceFeePercentage = ServiceFeePercentage,
		PaymentGatewayFeePercentage = PaymentGatewayFeePercentage,
		TotalFeePercentage = TotalFeePercentage,
		IsPopular = IsPopular,
		Description = Description
	};
}
