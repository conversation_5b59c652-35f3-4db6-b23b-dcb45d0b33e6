{"openapi": "3.0.1", "info": {"title": "User API", "version": "v1", "description": "API for user management in Omani Job Transfer Application"}, "servers": [{"url": "https://localhost:5002", "description": "Local development server"}], "paths": {"/api/user": {"get": {"summary": "Get all users", "operationId": "GetUsers", "tags": ["User"], "responses": {"200": {"description": "A list of users", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}}}}, "post": {"summary": "Create a new user", "operationId": "CreateUser", "tags": ["User"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "responses": {"201": {"description": "User created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}}}, "/api/user/{id}": {"get": {"summary": "Get user by ID", "operationId": "GetUserById", "tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "404": {"description": "User not found"}}}, "put": {"summary": "Update user", "operationId": "UpdateUser", "tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "responses": {"200": {"description": "User updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}}}}, "components": {"schemas": {"User": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "firstName": {"type": "string"}, "secondName": {"type": "string"}, "tribe": {"type": "string"}, "fullName": {"type": "string"}, "email": {"type": "string", "format": "email"}, "phoneNumber": {"type": "string"}, "age": {"type": "integer"}, "gender": {"type": "string"}, "currentEmployerLocation": {"type": "string"}, "currentEmployerCity": {"type": "string"}, "currentEmployerState": {"type": "string"}, "currentSalaryGrade": {"type": "string"}, "educationalQualification": {"type": "string"}, "subscriptionTier": {"type": "string"}, "tokenBalance": {"type": "integer"}, "profileImageUrl": {"type": "string"}, "description": {"type": "string"}, "joinDate": {"type": "string", "format": "date-time"}, "isActive": {"type": "boolean"}, "referralCode": {"type": "string"}, "invitedFriends": {"type": "integer"}, "completedTransfers": {"type": "integer"}, "currentEmployer": {"type": "string"}, "currentPosition": {"type": "string"}, "yearsOfExperience": {"type": "integer"}, "skills": {"type": "array", "items": {"type": "string"}}, "languages": {"type": "array", "items": {"type": "string"}}, "certifications": {"type": "array", "items": {"type": "string"}}, "preferredWorkType": {"type": "string", "enum": ["office", "remote", "hybrid"]}, "isAvailableForTransfer": {"type": "boolean"}, "linkedInProfile": {"type": "string"}, "portfolio": {"type": "string"}, "resumeUrl": {"type": "string"}, "bio": {"type": "string"}}, "required": ["email", "fullName"]}}}}