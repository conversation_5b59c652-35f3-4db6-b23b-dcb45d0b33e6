using System.Reflection;

namespace JT.Server.Controllers;

public abstract class ChefsControllerBase : ControllerBase
{
	//load data from embedded resource or file system
	protected async Task<T> GetMockData<T>(string fileName)
	{
		// First try to load from file system (for development) - relative to project root
		var filePath = Path.Combine("..", "AppData", fileName);
		if (File.Exists(filePath))
		{
			var json = await File.ReadAllTextAsync(filePath);
			return JsonSerializer.Deserialize<T>(json) ?? throw new JsonException($"Failed to deserialize JSON from '{filePath}'.");
		}

		// Fallback to embedded resource (configured in .csproj as LinkBase="AppData")
		var assembly = Assembly.GetExecutingAssembly();
		var resourceName = $"JT.Server.AppData.{fileName}";
		using var stream = assembly.GetManifestResourceStream(resourceName);
		if (stream == null) throw new FileNotFoundException($"Resource '{resourceName}' not found.");
		using var reader = new StreamReader(stream);
		var jsonContent = await reader.ReadToEndAsync();
		return JsonSerializer.Deserialize<T>(jsonContent) ?? throw new JsonException($"Failed to deserialize JSON from '{resourceName}'.");
	}

	protected async Task SaveMockData<T>(string fileName, T data)
	{
		// Save to the root AppData folder (relative to project root)
		var filePath = Path.Combine("..", "AppData", fileName);
		var directory = Path.GetDirectoryName(filePath);
		if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
		{
			Directory.CreateDirectory(directory);
		}

		var json = JsonSerializer.Serialize(data, new JsonSerializerOptions { WriteIndented = true });
		await File.WriteAllTextAsync(filePath, json);
	}

	//load data from embedded resource (legacy method for backward compatibility)
	protected T LoadData<T>(string fileName)
	{
		var assembly = Assembly.GetExecutingAssembly();
		var resourceName = $"Chefs.Api.AppData.{fileName}";
		using var stream = assembly.GetManifestResourceStream(resourceName);
		if (stream == null) throw new FileNotFoundException($"Resource '{resourceName}' not found.");
		using var reader = new StreamReader(stream);
		var json = reader.ReadToEnd();
		return JsonSerializer.Deserialize<T>(json) ?? throw new JsonException($"Failed to deserialize JSON from '{resourceName}'.");
	}
}
