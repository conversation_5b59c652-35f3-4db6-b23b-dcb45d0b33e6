﻿[
	{
		"Id": 1,
		"UrlIcon": "ms-appx:///Assets/Categories/breakfast.png",
		"Name": "Breakfast",
		"Color": "#7A67F8"
	},
	{
		"Id": 2,
		"UrlIcon": "ms-appx:///Assets/Categories/lunch.png",
		"Name": "Lunch",
		"Color": "#507FF7"
	},
	{
		"Id": 3,
		"UrlIcon": "ms-appx:///Assets/Categories/dinner.png",
		"Name": "Dinner",
		"Color": "#F16583"
	},
	{
		"Id": 4,
		"UrlIcon": "ms-appx:///Assets/Categories/snack.png",
		"Name": "Snack",
		"Color": "#CAC2FC"
	},
	{
		"Id": 5,
		"UrlIcon": "ms-appx:///Assets/Categories/healthy.png",
		"Name": "Healthy",
		"Color": "#0FD87E"
	},
	{
		"Id": 6,
		"UrlIcon": "ms-appx:///Assets/Categories/chinese.png",
		"Name": "Chinese",
		"Color": "#507FF7"
	},
	{
		"Id": 7,
		"UrlIcon": "ms-appx:///Assets/Categories/dessert.png",
		"Name": "Dessert",
		"Color": "#7A67F8"
	},
	{
		"Id": 8,
		"UrlIcon": "ms-appx:///Assets/Categories/mexican.png",
		"Name": "Mexican",
		"Color": "#CAC2FC"
	},
	{
		"Id": 9,
		"UrlIcon": "ms-appx:///Assets/Categories/pasta.png",
		"Name": "Pasta",
		"Color": "#F16583"
	},
	{
		"Id": 10,
		"UrlIcon": "ms-appx:///Assets/Categories/sushi.png",
		"Name": "Sushi",
		"Color": "#7A67F8"
	},
	{
		"Id": 11,
		"UrlIcon": "ms-appx:///Assets/Categories/vegan.png",
		"Name": "Veggie",
		"Color": "#507FF7"
	},
	{
		"Id": 12,
		"UrlIcon": "ms-appx:///Assets/Categories/poutine.png",
		"Name": "Poutine",
		"Color": "#0FD87E"
	}
]