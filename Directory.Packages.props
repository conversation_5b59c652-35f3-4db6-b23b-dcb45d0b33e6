<Project ToolsVersion="15.0">
  <!--
    To update the version of Uno, you should instead update the Sdk version in the global.json file.

    See https://aka.platform.uno/using-uno-sdk for more information.
    See https://aka.platform.uno/using-uno-sdk#implicit-packages for more information regarding the Implicit Packages.
  -->
  <ItemGroup>
    <PackageVersion Include="Abp.ZeroCore.IdentityServer4" Version="8.4.0" />
    <PackageVersion Include="coverlet.collector" Version="6.0.2" />
    <PackageVersion Include="LiveChartsCore.SkiaSharpView.Uno.WinUI" Version="2.0.0-rc4.5" />
    <PackageVersion Include="Mapsui.Uno.WinUI" Version="5.0.0-beta.4" />
    <PackageVersion Include="LiveChartsCore.SkiaSharpView.WinUI" Version="2.0.0-rc4.5" />
    <PackageVersion Include="Mapsui.WinUI" Version="5.0.0-beta.4" />
    <PackageVersion Include="FluentAssertions" Version="6.12.0" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.9.0" />
    <PackageVersion Include="NUnit" Version="4.1.0" />
    <PackageVersion Include="NUnit3TestAdapter" Version="4.5.0" />
    <PackageVersion Include="GitHubActionsTestLogger" Version="2.3.3" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageVersion Include="Uno.UITest.Helpers" Version="1.1.0-dev.70" />
    <PackageVersion Include="Xamarin.UITest" Version="4.3.4" />
  </ItemGroup>
  <ItemGroup Condition="$(UsingUnoSdk) != 'true'">
    <PackageVersion Include="Uno.Wasm.Bootstrap.Server" Version="9.0.19" />
  </ItemGroup>
</Project>