using JT.Server.Entities;
using Microsoft.AspNetCore.Mvc;

namespace JT.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TransferRequestController : ChefsControllerBase
{
	private readonly ILogger<TransferRequestController> _logger;

	public TransferRequestController(ILogger<TransferRequestController> logger)
	{
		_logger = logger;
	}

	[HttpGet]
	public async Task<ActionResult<IEnumerable<TransferRequestData>>> GetTransferRequests()
	{
		try
		{
			var transferRequests = await GetMockData<TransferRequestData>("TransferRequests.json");
			return Ok(transferRequests);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting transfer requests");
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("{id}")]
	public async Task<ActionResult<TransferRequestData>> GetTransferRequest(Guid id)
	{
		try
		{
			var transferRequests = await GetMockData<TransferRequestData>("TransferRequests.json");
			var transferRequest = transferRequests.FirstOrDefault(r => r.Id == id);
			
			if (transferRequest == null)
			{
				return NotFound();
			}

			return Ok(transferRequest);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting transfer request {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPost]
	public async Task<ActionResult<TransferRequestData>> CreateTransferRequest(TransferRequestData transferRequest)
	{
		try
		{
			transferRequest.Id = Guid.NewGuid();
			transferRequest.SubmissionDate = DateTime.UtcNow;
			transferRequest.Status = "Pending";
			transferRequest.ViewCount = 0;
			transferRequest.InterestedEmployers = 0;

			var transferRequests = await GetMockData<TransferRequestData>("TransferRequests.json");
			var updatedList = transferRequests.Append(transferRequest).ToList();
			await SaveMockData("TransferRequests.json", updatedList);

			return CreatedAtAction(nameof(GetTransferRequest), new { id = transferRequest.Id }, transferRequest);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error creating transfer request");
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPut("{id}")]
	public async Task<ActionResult<TransferRequestData>> UpdateTransferRequest(Guid id, TransferRequestData transferRequest)
	{
		try
		{
			var transferRequests = await GetMockData<TransferRequestData>("TransferRequests.json");
			var existingIndex = transferRequests.FindIndex(r => r.Id == id);
			
			if (existingIndex == -1)
			{
				return NotFound();
			}

			transferRequest.Id = id;
			transferRequests[existingIndex] = transferRequest;
			await SaveMockData("TransferRequests.json", transferRequests);

			return Ok(transferRequest);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error updating transfer request {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpDelete("{id}")]
	public async Task<ActionResult> DeleteTransferRequest(Guid id)
	{
		try
		{
			var transferRequests = await GetMockData<TransferRequestData>("TransferRequests.json");
			var existingIndex = transferRequests.FindIndex(r => r.Id == id);
			
			if (existingIndex == -1)
			{
				return NotFound();
			}

			transferRequests.RemoveAt(existingIndex);
			await SaveMockData("TransferRequests.json", transferRequests);

			return NoContent();
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error deleting transfer request {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("user/{userId}")]
	public async Task<ActionResult<IEnumerable<TransferRequestData>>> GetUserTransferRequests(Guid userId)
	{
		try
		{
			var transferRequests = await GetMockData<TransferRequestData>("TransferRequests.json");
			var userRequests = transferRequests.Where(r => r.UserId == userId).ToList();
			return Ok(userRequests);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting transfer requests for user {UserId}", userId);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("status/{status}")]
	public async Task<ActionResult<IEnumerable<TransferRequestData>>> GetTransferRequestsByStatus(string status)
	{
		try
		{
			var transferRequests = await GetMockData<TransferRequestData>("TransferRequests.json");
			var filteredRequests = transferRequests.Where(r => 
				string.Equals(r.Status, status, StringComparison.OrdinalIgnoreCase)).ToList();
			return Ok(filteredRequests);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting transfer requests by status {Status}", status);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPost("{id}/increment-view")]
	public async Task<ActionResult> IncrementViewCount(Guid id)
	{
		try
		{
			var transferRequests = await GetMockData<TransferRequestData>("TransferRequests.json");
			var existingIndex = transferRequests.FindIndex(r => r.Id == id);
			
			if (existingIndex == -1)
			{
				return NotFound();
			}

			transferRequests[existingIndex].ViewCount++;
			await SaveMockData("TransferRequests.json", transferRequests);

			return Ok();
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error incrementing view count for transfer request {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}
}
