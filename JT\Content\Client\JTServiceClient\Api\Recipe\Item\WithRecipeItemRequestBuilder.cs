// <auto-generated/>
#pragma warning disable CS0618
using JT.Client.Api.Recipe.Item.Ingredients;
using JT.Client.Api.Recipe.Item.Reviews;
using JT.Client.Api.Recipe.Item.Steps;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
namespace JT.Client.Api.Recipe.Item
{
    /// <summary>
    /// Builds and executes requests for operations under \api\Recipe\{recipeId}
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class WithRecipeItemRequestBuilder : BaseRequestBuilder
    {
        /// <summary>The ingredients property</summary>
        public global::JT.Client.Api.Recipe.Item.Ingredients.IngredientsRequestBuilder Ingredients
        {
            get => new global::JT.Client.Api.Recipe.Item.Ingredients.IngredientsRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The reviews property</summary>
        public global::JT.Client.Api.Recipe.Item.Reviews.ReviewsRequestBuilder Reviews
        {
            get => new global::JT.Client.Api.Recipe.Item.Reviews.ReviewsRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The steps property</summary>
        public global::JT.Client.Api.Recipe.Item.Steps.StepsRequestBuilder Steps
        {
            get => new global::JT.Client.Api.Recipe.Item.Steps.StepsRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Client.Api.Recipe.Item.WithRecipeItemRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public WithRecipeItemRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/Recipe/{recipeId}", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Client.Api.Recipe.Item.WithRecipeItemRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public WithRecipeItemRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/Recipe/{recipeId}", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
